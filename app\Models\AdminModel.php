<?php

namespace App\Models;

use CodeIgniter\Model;

class AdminModel extends Model
{
    protected $table = 'admins';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'username',
        'email',
        'password',
        'full_name',
        'role',
        'status',
        'last_login',
        'login_attempts',
        'locked_until'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'username' => 'required|min_length[3]|max_length[50]|is_unique[admins.username,id,{id}]',
        'email'    => 'required|valid_email|is_unique[admins.email,id,{id}]',
        'password' => 'required|min_length[8]',
        'full_name' => 'required|min_length[2]|max_length[100]',
        'role'     => 'required|in_list[super_admin,admin,moderator]',
        'status'   => 'required|in_list[active,inactive,suspended]'
    ];

    protected $validationMessages = [
        'username' => [
            'required'   => 'Username is required',
            'min_length' => 'Username must be at least 3 characters',
            'is_unique'  => 'Username already exists'
        ],
        'email' => [
            'required'    => 'Email is required',
            'valid_email' => 'Please enter a valid email',
            'is_unique'   => 'Email already exists'
        ],
        'password' => [
            'required'   => 'Password is required',
            'min_length' => 'Password must be at least 8 characters'
        ]
    ];

    // Skip validation
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    /**
     * Hash password before saving
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }
        return $data;
    }

    /**
     * Verify admin credentials
     */
    public function verifyCredentials($username, $password)
    {
        $admin = $this->where('username', $username)
                     ->orWhere('email', $username)
                     ->first();

        if (!$admin) {
            return false;
        }

        // Check if account is locked
        if ($admin['locked_until'] && strtotime($admin['locked_until']) > time()) {
            return ['error' => 'Account is temporarily locked. Please try again later.'];
        }

        // Check if account is active
        if ($admin['status'] !== 'active') {
            return ['error' => 'Account is not active. Please contact administrator.'];
        }

        // Verify password
        if (password_verify($password, $admin['password'])) {
            // Reset login attempts on successful login
            $this->update($admin['id'], [
                'login_attempts' => 0,
                'locked_until' => null,
                'last_login' => date('Y-m-d H:i:s')
            ]);
            
            unset($admin['password']); // Remove password from returned data
            return $admin;
        }

        // Increment login attempts
        $attempts = $admin['login_attempts'] + 1;
        $updateData = ['login_attempts' => $attempts];

        // Lock account after 5 failed attempts for 30 minutes
        if ($attempts >= 5) {
            $updateData['locked_until'] = date('Y-m-d H:i:s', strtotime('+30 minutes'));
        }

        $this->update($admin['id'], $updateData);

        return false;
    }

    /**
     * Create default admin if none exists
     */
    public function createDefaultAdmin()
    {
        $count = $this->countAllResults();

        if ($count === 0) {
            // Temporarily skip validation for default admin creation
            $this->skipValidation(true);

            $result = $this->insert([
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => 'TradeDiary@2025', // Will be hashed by callback
                'full_name' => 'System Administrator',
                'role' => 'super_admin',
                'status' => 'active'
            ]);

            // Re-enable validation
            $this->skipValidation(false);

            return $result !== false;
        }

        return false;
    }

    /**
     * Get admin permissions based on role
     */
    public function getPermissions($role)
    {
        $permissions = [
            'super_admin' => [
                'manage_admins',
                'manage_payouts',
                'manage_users',
                'view_reports',
                'export_data',
                'system_settings'
            ],
            'admin' => [
                'manage_payouts',
                'manage_users',
                'view_reports',
                'export_data'
            ],
            'moderator' => [
                'view_payouts',
                'view_users',
                'view_reports'
            ]
        ];

        return $permissions[$role] ?? [];
    }
}
