<?php

namespace App\Models;

use CodeIgniter\Model;

class AdminModel extends Model
{
    protected $table = 'admins';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'username',
        'email',
        'password',
        'full_name',
        'role',
        'status',
        'last_login'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'username' => 'required|min_length[3]|max_length[50]|is_unique[admins.username,id,{id}]',
        'email'    => 'required|valid_email|is_unique[admins.email,id,{id}]',
        'password' => 'required|min_length[8]',
        'full_name' => 'required|min_length[2]|max_length[100]',
        'role'     => 'required|in_list[super_admin,admin,moderator]',
        'status'   => 'required|in_list[active,inactive,suspended]'
    ];

    protected $validationMessages = [
        'username' => [
            'required'   => 'Username is required',
            'min_length' => 'Username must be at least 3 characters',
            'is_unique'  => 'Username already exists'
        ],
        'email' => [
            'required'    => 'Email is required',
            'valid_email' => 'Please enter a valid email',
            'is_unique'   => 'Email already exists'
        ],
        'password' => [
            'required'   => 'Password is required',
            'min_length' => 'Password must be at least 8 characters'
        ]
    ];

    // Skip validation
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    /**
     * Hash password before saving
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }
        return $data;
    }

    /**
     * Verify admin credentials
     */
    public function verifyCredentials($username, $password)
    {
        log_message('debug', 'AdminModel::verifyCredentials called with username: ' . $username);

        $admin = $this->where('username', $username)
                     ->orWhere('email', $username)
                     ->first();

        log_message('debug', 'Admin found: ' . ($admin ? 'YES' : 'NO'));

        if (!$admin) {
            log_message('debug', 'Admin not found in database');
            return false;
        }

        log_message('debug', 'Admin status: ' . $admin['status']);

        // Check if account is active
        if ($admin['status'] !== 'active') {
            log_message('debug', 'Account not active');
            return ['error' => 'Account is not active. Please contact administrator.'];
        }

        // Verify password
        $passwordMatch = password_verify($password, $admin['password']);
        log_message('debug', 'Password verification: ' . ($passwordMatch ? 'SUCCESS' : 'FAILED'));
        log_message('debug', 'Stored hash: ' . substr($admin['password'], 0, 20) . '...');

        if ($passwordMatch) {
            log_message('debug', 'Login successful for user: ' . $username);
            // Update last login on successful login
            $this->update($admin['id'], [
                'last_login' => date('Y-m-d H:i:s')
            ]);

            unset($admin['password']); // Remove password from returned data
            return $admin;
        }

        log_message('debug', 'Password verification failed');
        return false;
    }

    /**
     * Create default admin if none exists
     */
    public function createDefaultAdmin()
    {
        try {
            $count = $this->countAllResults();

            if ($count === 0) {
                // Temporarily skip validation for default admin creation
                $this->skipValidation(true);

                $result = $this->insert([
                    'username' => 'admin',
                    'email' => '<EMAIL>',
                    'password' => 'TradeDiary@2025', // Will be hashed by callback
                    'full_name' => 'System Administrator',
                    'role' => 'super_admin',
                    'status' => 'active'
                ]);

                // Re-enable validation
                $this->skipValidation(false);

                return $result !== false;
            }

            return false;
        } catch (\Exception $e) {
            // Log error but don't break the application
            log_message('error', 'Failed to create default admin: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Force recreate default admin (for debugging)
     */
    public function forceCreateDefaultAdmin()
    {
        try {
            // Delete existing admin if any
            $this->where('username', 'admin')->delete();

            // Temporarily skip validation
            $this->skipValidation(true);

            $result = $this->insert([
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => 'TradeDiary@2025', // Will be hashed by callback
                'full_name' => 'System Administrator',
                'role' => 'super_admin',
                'status' => 'active'
            ]);

            // Re-enable validation
            $this->skipValidation(false);

            return $result !== false;
        } catch (\Exception $e) {
            log_message('error', 'Failed to force create default admin: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get admin permissions based on role
     */
    public function getPermissions($role)
    {
        $permissions = [
            'super_admin' => [
                'manage_admins',
                'manage_payouts',
                'manage_users',
                'view_reports',
                'export_data',
                'system_settings'
            ],
            'admin' => [
                'manage_payouts',
                'manage_users',
                'view_reports',
                'export_data'
            ],
            'moderator' => [
                'view_payouts',
                'view_users',
                'view_reports'
            ]
        ];

        return $permissions[$role] ?? [];
    }

    /**
     * Update default admin password to new credentials
     */
    public function updateDefaultAdminPassword()
    {
        try {
            $admin = $this->where('username', 'admin')->first();

            if ($admin) {
                // Update password to new one
                $result = $this->update($admin['id'], [
                    'password' => 'TradeDiary@2025' // Will be hashed by callback
                ]);

                return $result !== false;
            }

            return false;
        } catch (\Exception $e) {
            log_message('error', 'Failed to update default admin password: ' . $e->getMessage());
            return false;
        }
    }
}
