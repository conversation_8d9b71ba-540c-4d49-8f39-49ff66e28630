/* ===================================
   ADMIN PANEL CSS FRAMEWORK
   Dark Mode & Responsive Design
   =================================== */

/* CSS Variables for Theme Management */
:root {
    /* Light Theme Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-tertiary: #9ca3af;
    --border-primary: #e5e7eb;
    --border-secondary: #d1d5db;
    --accent-primary: #6366f1;
    --accent-secondary: #4f46e5;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Sidebar Colors */
    --sidebar-bg: #1f2937;
    --sidebar-bg-dark: #111827;
    --sidebar-text: #d1d5db;
    --sidebar-text-active: #ffffff;
    --sidebar-hover: #374151;
    --sidebar-active: #6366f1;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --bg-primary: #1f2937;
    --bg-secondary: #111827;
    --bg-tertiary: #0f172a;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-tertiary: #9ca3af;
    --border-primary: #374151;
    --border-secondary: #4b5563;
    --accent-primary: #6366f1;
    --accent-secondary: #4f46e5;
    
    /* Sidebar Colors for Dark Mode */
    --sidebar-bg: #0f172a;
    --sidebar-bg-dark: #020617;
    --sidebar-text: #cbd5e1;
    --sidebar-text-active: #ffffff;
    --sidebar-hover: #1e293b;
    --sidebar-active: #6366f1;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===================================
   SIDEBAR STYLES
   =================================== */

.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    transition: transform var(--transition-normal), width var(--transition-normal);
    box-shadow: var(--shadow-lg);
    z-index: 50;
}

.sidebar-header {
    background-color: var(--sidebar-bg-dark);
    border-bottom: 1px solid var(--border-primary);
    padding: 1rem 1.5rem;
}

.sidebar-collapsed {
    width: 4rem;
}

.sidebar-collapsed .sidebar-text {
    opacity: 0;
    visibility: hidden;
}

.sidebar-collapsed .sidebar-icon {
    margin-right: 0;
}

/* Navigation Links */
.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    margin: 0.125rem 0.5rem;
    border-radius: 0.5rem;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.nav-link:hover {
    background-color: var(--sidebar-hover);
    color: var(--sidebar-text-active);
    transform: translateX(4px);
}

.nav-link.active {
    background-color: var(--sidebar-active);
    color: var(--sidebar-text-active);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: var(--sidebar-text-active);
}

.nav-icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    transition: transform var(--transition-fast);
    flex-shrink: 0;
}

.nav-link:hover .nav-icon {
    transform: scale(1.1);
}

.nav-text {
    transition: opacity var(--transition-normal), visibility var(--transition-normal);
    white-space: nowrap;
}

/* Section Headers */
.nav-section-header {
    padding: 0.75rem 1rem;
    margin: 1rem 0.5rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--text-tertiary);
    border-bottom: 1px solid var(--border-primary);
}

/* ===================================
   TOPBAR STYLES
   =================================== */

.topbar {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

/* Dark Mode Toggle */
.theme-toggle {
    position: relative;
    width: 3rem;
    height: 1.5rem;
    background-color: #d1d5db;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: background-color var(--transition-normal);
    border: none;
    outline: none;
    display: flex;
    align-items: center;
}

.theme-toggle:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.theme-toggle:hover {
    background-color: #9ca3af;
}

.theme-toggle.dark {
    background-color: var(--accent-primary);
}

.theme-toggle.dark:hover {
    background-color: var(--accent-secondary);
}

.theme-toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 1.25rem;
    height: 1.25rem;
    background-color: white;
    border-radius: 50%;
    transition: transform var(--transition-normal), background-color var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.theme-toggle.dark .theme-toggle-slider {
    transform: translateX(1.5rem);
    background-color: #f8fafc;
}

.theme-icon {
    font-size: 0.75rem;
    color: #fbbf24;
    transition: color var(--transition-normal);
}

.theme-toggle.dark .theme-icon {
    color: #1e293b;
}

/* ===================================
   BUTTON STYLES
   =================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    border: none;
    cursor: pointer;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--error) 0%, #dc2626 100%);
    color: white;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--border-primary);
}

/* ===================================
   CARD STYLES
   =================================== */

.card {
    background-color: var(--bg-primary);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-primary);
    transition: all var(--transition-normal);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-primary);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-primary);
    background-color: var(--bg-tertiary);
}

/* ===================================
   TABLE STYLES
   =================================== */

.table-container {
    background-color: var(--bg-primary);
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-primary);
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background-color: var(--bg-tertiary);
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-primary);
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-primary);
}

.table tbody tr {
    transition: background-color var(--transition-fast);
}

.table tbody tr:hover {
    background-color: var(--bg-tertiary);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* ===================================
   FORM STYLES
   =================================== */

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-primary);
    border-radius: 0.5rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* ===================================
   STATUS BADGES
   =================================== */

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-approved {
    background-color: #d1fae5;
    color: #065f46;
}

.status-rejected {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-processing {
    background-color: #dbeafe;
    color: #1e40af;
}

/* Dark mode status badges */
[data-theme="dark"] .status-pending {
    background-color: #451a03;
    color: #fbbf24;
}

[data-theme="dark"] .status-approved {
    background-color: #064e3b;
    color: #34d399;
}

[data-theme="dark"] .status-rejected {
    background-color: #7f1d1d;
    color: #f87171;
}

[data-theme="dark"] .status-processing {
    background-color: #1e3a8a;
    color: #60a5fa;
}

/* ===================================
   ANIMATIONS & TRANSITIONS
   =================================== */

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

/* ===================================
   LOADING STATES
   =================================== */

.loading-spinner {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid var(--border-primary);
    border-top: 2px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background-color: var(--bg-primary);
    padding: 2rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: var(--shadow-lg);
}

/* ===================================
   TOAST NOTIFICATIONS
   =================================== */

.toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 9999;
    min-width: 300px;
    padding: 1rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 500;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    box-shadow: var(--shadow-lg);
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
}

.toast-error {
    background: linear-gradient(135deg, var(--error) 0%, #dc2626 100%);
}

.toast-warning {
    background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);
}

.toast-info {
    background: linear-gradient(135deg, var(--info) 0%, #2563eb 100%);
}

/* ===================================
   DROPDOWN MENUS
   =================================== */

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 12rem;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-lg);
    z-index: 50;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color var(--transition-fast);
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
}

.dropdown-item:hover {
    background-color: var(--bg-tertiary);
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-primary);
    margin: 0.5rem 0;
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

/* Mobile Sidebar Overlay */
.sidebar-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .sidebar {
        position: fixed;
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }
}

@media (min-width: 1024px) {
    .main-content {
        margin-left: 16rem;
        transition: margin-left var(--transition-normal);
    }

    .main-content.sidebar-collapsed {
        margin-left: 4rem;
    }
}

/* Tablet Responsive */
@media (max-width: 768px) {
    .topbar {
        padding: 0.75rem 1rem;
    }

    .card {
        margin: 0.5rem;
        border-radius: 0.5rem;
    }

    .table-container {
        margin: 0.5rem;
        border-radius: 0.5rem;
    }

    .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }
}

/* ===================================
   UTILITY CLASSES
   =================================== */

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-wrap {
    word-wrap: break-word;
    word-break: break-word;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.pointer-events-none {
    pointer-events: none;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-not-allowed {
    cursor: not-allowed;
}

/* ===================================
   ACCESSIBILITY
   =================================== */

.focus-visible:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===================================
   ADDITIONAL DARK MODE OVERRIDES
   =================================== */

/* Ensure proper dark mode inheritance */
[data-theme="dark"] {
    color-scheme: dark;
}

[data-theme="dark"] .bg-gray-100 {
    background-color: var(--bg-tertiary) !important;
}

[data-theme="dark"] .text-gray-600 {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .text-gray-400 {
    color: var(--text-tertiary) !important;
}

[data-theme="dark"] .border-gray-300 {
    border-color: var(--border-secondary) !important;
}

[data-theme="dark"] .hover\:bg-gray-100:hover {
    background-color: var(--bg-tertiary) !important;
}

[data-theme="dark"] .hover\:bg-gray-50:hover {
    background-color: var(--bg-secondary) !important;
}

/* Dark mode for specific components */
[data-theme="dark"] .shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
}

/* Ensure dropdown menus work in dark mode */
[data-theme="dark"] .dropdown-menu {
    background-color: var(--bg-primary);
    border-color: var(--border-primary);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
}

/* Fix for Tailwind utility classes in dark mode */
[data-theme="dark"] .divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
    border-color: var(--border-primary);
}

[data-theme="dark"] .border-t {
    border-top-color: var(--border-primary);
}

[data-theme="dark"] .border-b {
    border-bottom-color: var(--border-primary);
}

[data-theme="dark"] .border-l {
    border-left-color: var(--border-primary);
}

[data-theme="dark"] .border-r {
    border-right-color: var(--border-primary);
}
