let pnlChartFilter = 'D';
let currencySymbol = '₹';

$('#rangeFilter').change(function () {
    getDashboardMetrics()
    fetchTopTrades()
    renderWinLossChart()
    showTraderConfidence()
    renderStrategyPnlChart()
    renderMostCommonMistakes()
})

$('#marketTypeFilter').change(function () {
    if ($(this).val() == 1) {
        currencySymbol = '₹';
        $('.currencySymbol').text(currencySymbol)
    }
    else{
        currencySymbol = '$';
        $('.currencySymbol').text(currencySymbol)
    }
    getDashboardMetrics()
    // loadTrades();
    getEquityChartData()
    fetchTopTrades()
    renderWinLossChart()
    showTraderConfidence()
    renderDailyPnlChart()
    renderStrategyPnlChart()
    renderMostCommonMistakes()
})

$(document).ready(function () {
    getDashboardMetrics()
    loadTrades();
    getEquityChartData()
    fetchTopTrades()
    renderWinLossChart()
    showTraderConfidence()
    renderDailyPnlChart()
    renderStrategyPnlChart()
    renderMostCommonMistakes()
});

let winLossChart;

function renderWinLossChart() {
    let rangeFilter = $('#rangeFilter').val()
    let marketTypeFilter = $('#marketTypeFilter').val()
    $.ajax({
        type: 'POST',
        url: base_url + 'getWinLossChartData',
        data: { rangeFilter, marketTypeFilter },
        dataType: 'JSON',
        success: function (res) {
            const ctx = document.getElementById('winLossChart').getContext('2d');

            // ✅ Destroy previous chart if it exists
            if (winLossChart) {
                winLossChart.destroy();
            }

            const isNoData = !res.data || (res.data.counts[0] === 0 && res.data.counts[1] === 0);

            if (isNoData) {
                $('#winLossChartContainer').html(`
        <div class="flex flex-col justify-center items-center h-full py-16 text-center text-gray-500 dark:text-gray-400">
            <i class="fas fa-balance-scale text-6xl mb-4 text-gray-300 dark:text-gray-600"></i>
            <p class="text-lg font-semibold">No trade outcomes yet</p>
            <p class="text-sm mt-1 text-gray-400">
                Once you start trading, your wins and losses will appear here.<br>
                Stay consistent and track your edge!
            </p>
        </div>
    `);
                return;
            }
            // ✅ Create new chart
            winLossChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: res.labels,
                    datasets: [{
                        data: res.data.percentages,
                        counts: res.data.counts, // Pass raw counts
                        backgroundColor: ['#10b981', '#ef4444'],
                        borderWidth: 0,
                        hoverOffset: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                pointStyle: 'circle',
                                color: '#64748b'
                            }
                        },
                        tooltip: {
                            backgroundColor: '#1e293b',
                            titleColor: '#f8fafc',
                            bodyColor: '#e2e8f0',
                            borderColor: '#334155',
                            borderWidth: 1,
                            padding: 12,
                            callbacks: {
                                label: function (context) {
                                    // Show count instead of percentage
                                    const count = context.dataset.counts[context.dataIndex];
                                    return `${context.label}: ${count} trades`;
                                }
                            }
                        }
                    }
                }
            });
        }
    });
}

let strategyPnlChart;

function renderStrategyPnlChart() {
    let rangeFilter = $('#rangeFilter').val()
    let marketTypeFilter = $('#marketTypeFilter').val()
    $.ajax({
        type: "POST",
        url: base_url + "getStrategyPnls",
        data: { rangeFilter, marketTypeFilter },
        dataType: "JSON",
        success: function (res) {
            const ctx = document.getElementById('strategyPnlChart')?.getContext('2d');
            if (!ctx) return;

            if (strategyPnlChart && typeof strategyPnlChart.destroy === 'function') {
                strategyPnlChart.destroy();
            }

            if (!res.data || res.data.length === 0) {
                $('#strategyPnlChart').parent().html(`
        <div class="flex flex-col justify-center items-center h-full py-16 text-center text-gray-500 dark:text-gray-400">
    <i class="fas fa-lightbulb text-6xl mb-4 text-gray-300 dark:text-gray-600"></i>
    <p class="text-lg font-semibold">No strategy insights yet</p>
    <p class="text-sm mt-1 text-gray-400">
        Tag your trades with strategies to analyze what's working best.<br>
        Insight begins with tracking.
    </p>
</div>

    `);
                return;
            }

            // Conditional colors based on P&L
            const barColors = res.data.map(value => value >= 0 ? '#a855f7' : '#fb923c');

            strategyPnlChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: res.labels,
                    datasets: [{
                        label: 'Total P&L (Last 1 Month)',
                        data: res.data,
                        backgroundColor: barColors,
                        borderRadius: 6,
                        borderWidth: 0,
                        maxBarThickness: 40 // 🔽 This limits the width of each bar
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                        padding: {
                            left: 20,  // 👈 Adjust as needed
                            right: 20
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: '#1e293b',
                            titleColor: '#f8fafc',
                            bodyColor: '#e2e8f0',
                            borderColor: '#334155',
                            borderWidth: 1,
                            padding: 12,
                            callbacks: {
                                label: function (context) {
                                    return `${currencySymbol}${context.raw}`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            // offset: false,
                            grid: { display: false },
                            ticks: {
                                color: '#64748b'
                            }
                        },
                        y: {
                            grid: {
                                color: '#e2e8f0'
                            },
                            ticks: {
                                color: '#64748b',
                                callback: function (value) {
                                    return `${currencySymbol}${value}`;
                                }
                            }
                        }
                    }
                }
            });
        }
    });
}


let strategyChart;

function renderStrategyChart() {
    $.ajax({
        type: "POST",
        url: base_url + "getStrategyPerformance",
        dataType: "JSON",
        success: function (res) {
            const ctx = document.getElementById('strategyChart')?.getContext('2d');

            if (!ctx) return; // canvas not found

            // Safely destroy previous chart if it exists
            if (strategyChart && typeof strategyChart.destroy === 'function') {
                strategyChart.destroy();
            }

            strategyChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: res.labels,
                    datasets: [
                        {
                            label: 'Win Rate',
                            data: res.winRates,
                            backgroundColor: '#3b82f6',
                            borderRadius: 6,
                            borderWidth: 0
                        },
                        {
                            label: 'Avg. P&L',
                            data: res.avgPnls,
                            backgroundColor: '#10b981',
                            borderRadius: 6,
                            borderWidth: 0
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                pointStyle: 'circle',
                                color: '#64748b'
                            }
                        },
                        tooltip: {
                            backgroundColor: '#1e293b',
                            titleColor: '#f8fafc',
                            bodyColor: '#e2e8f0',
                            borderColor: '#334155',
                            borderWidth: 1,
                            padding: 12,
                            callbacks: {
                                label: function (context) {
                                    if (context.dataset.label === 'Win Rate') {
                                        return `${context.dataset.label}: ${context.raw}%`;
                                    } else {
                                        return `${context.dataset.label}: ${currencySymbol}${context.raw}`;
                                    }
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: { display: false, drawBorder: false },
                            ticks: { color: '#64748b' }
                        },
                        y: {
                            grid: {
                                color: '#e2e8f0',
                                drawBorder: false
                            },
                            ticks: {
                                color: '#64748b'
                            }
                        }
                    }
                }
            });
        }
    });
}

function renderDailyPnlChart() {
    let marketTypeFilter = $('#marketTypeFilter').val()
    $.ajax({
        type: "POST",
        url: base_url + "getDailyPnl",
        data: {marketTypeFilter},
        dataType: "JSON",
        success: function (res) {
            const ctx = document.getElementById('dailyPnlChart')?.getContext('2d');
            if (!ctx) return;

            if (dailyPnlChart && typeof dailyPnlChart.destroy === 'function') {
                dailyPnlChart.destroy();
            }

            if (!res.pnls || res.pnls.length === 0) {
                $('#dailyPnlChart').parent().html(`
        <div class="flex flex-col justify-center items-center h-full py-16 text-center text-gray-500 dark:text-gray-400">
    <i class="fas fa-calendar-day text-6xl mb-4 text-gray-300 dark:text-gray-600"></i>
    <p class="text-lg font-semibold">No daily performance yet</p>
    <p class="text-sm mt-1 text-gray-400">
        Start recording your trades to visualize your daily profit and loss trends.<br>
        Each day tells a story.
    </p>
</div>

    `);
                return;
            }

            const barColors = res.pnls.map(pnl => pnl >= 0 ? '#10b981' : '#ef4444');

            dailyPnlChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: res.labels,
                    datasets: [
                        {
                            label: 'Daily P&L ('+ currencySymbol +')',
                            data: res.pnls,
                            backgroundColor: barColors,
                            borderRadius: 6,
                            borderWidth: 0,
                            barPercentage: 0.6,
                            categoryPercentage: 0.6,
                            maxBarThickness: 40
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                        padding: {
                            left: 20,  // 👈 Adjust as needed
                            right: 20
                        }
                    },
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            backgroundColor: '#1e293b',
                            titleColor: '#f8fafc',
                            bodyColor: '#e2e8f0',
                            borderColor: '#334155',
                            borderWidth: 1,
                            padding: 12,
                            callbacks: {
                                label: context => `${currencySymbol}${context.raw}`
                            }
                        }
                    },
                    scales: {
                        x: {
                            // offset: false,
                            grid: { display: false, drawBorder: false },
                            ticks: {
                                color: '#64748b',
                                autoSkip: false
                            }
                        },
                        y: {
                            ticks: {
                                color: '#64748b',
                                callback: val => currencySymbol + val
                            },
                            grid: {
                                color: '#e2e8f0',
                                drawBorder: false
                            }
                        }
                    }
                }
            });
        }
    });
}



// Update charts on theme change
darkModeToggle.addEventListener('change', function () {
    pnlChart.update();
    winLossChart.update();
    strategyChart.update();
});

// Simulate market sentiment movement
// setInterval(() => {
//     const indicator = document.querySelector('.sentiment-indicator');
//     const currentPos = parseInt(indicator.style.left || '65');
//     const newPos = Math.min(Math.max(currentPos + (Math.random() * 6 - 3), 0), 100);
//     indicator.style.left = `${newPos}%`;

//     // Update sentiment text
//     const sentimentText = document.querySelector('.text-center.text-sm');
//     let sentiment = '';
//     if (newPos < 30) sentiment = 'Strongly Bearish';
//     else if (newPos < 45) sentiment = 'Bearish';
//     else if (newPos < 55) sentiment = 'Neutral';
//     else if (newPos < 70) sentiment = 'Moderately Bullish';
//     else sentiment = 'Strongly Bullish';

//     sentimentText.textContent = `Current sentiment: ${sentiment} (${Math.round(newPos)}%)`;
// }, 3000);

function showTraderConfidence() {
    let rangeFilter = $('#rangeFilter').val()
    let marketTypeFilter = $('#marketTypeFilter').val()

    $('#confIndText').text($('#rangeFilter option:selected').text());

    $.ajax({
        type: "POST",
        url: base_url + "getMonthlyConfidenceScore",
        data: { rangeFilter, marketTypeFilter },
        dataType: "JSON",
        success: function (response) {
            $('#confidence_progress').css('left', `${response.score}%`);
            $('#confidence_message').text(response.message)
        }
    });
}

// Modal functionality
const addTradeBtn = document.getElementById('addTradeBtn');
const addTradeBtn2 = document.getElementById('addTradeBtn2');
const addTradeModal = document.getElementById('addTradeModal');
const closeTradeModal = document.getElementById('closeTradeModal');
const resetFormBtn = document.getElementById('resetFormBtn');
const saveTradeBtn = document.getElementById('saveTradeBtn');
const successModal = document.getElementById('successModal');
const closeSuccessModal = document.getElementById('closeSuccessModal');



// Open add trade modal
addTradeBtn2.addEventListener('click', () => {
    addTradeModal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    $('#trade-tab').trigger('click')
});

closeSuccessModal.addEventListener('click', () => {
    successModal.style.display = 'none';
    addTradeModal.style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('tradeForm').reset();
    document.getElementById('confidenceValue').textContent = '5';
    document.getElementById('executionValue').textContent = '5';
    document.getElementById('previewContainer').innerHTML = '';
    selectedRuleIds.clear();
    renderSelectedRules();
});

addTradeBtn.addEventListener('click', () => {
    addTradeModal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    $('#trade-tab').trigger('click')
});

// Close add trade modal
closeTradeModal.addEventListener('click', () => {
    addTradeModal.style.display = 'none';
    document.body.style.overflow = 'auto';
});

// Close modal when clicking outside content
addTradeModal.addEventListener('click', (e) => {
    if (e.target === addTradeModal) {
        addTradeModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
});

// Reset form
resetFormBtn.addEventListener('click', () => {
    document.getElementById('tradeForm').reset();
    document.getElementById('confidenceValue').textContent = '5';
    document.getElementById('executionValue').textContent = '5';
    document.getElementById('previewContainer').innerHTML = '';
    selectedRuleIds.clear();
    renderSelectedRules();
});

// Tab switching
const tabButtons = document.querySelectorAll('.nav-tab');
const tabContents = document.querySelectorAll('.tab-content');

tabButtons.forEach(button => {
    button.addEventListener('click', () => {
        const tabId = button.getAttribute('data-tab');

        // Update active tab button
        tabButtons.forEach(btn => {
            btn.classList.remove('active', 'text-gray-800', 'dark:text-gray-200');
            btn.classList.add('text-gray-500', 'dark:text-gray-400');
        });
        button.classList.add('active', 'text-gray-800', 'dark:text-gray-200');
        button.classList.remove('text-gray-500', 'dark:text-gray-400');

        // Update active tab content
        tabContents.forEach(content => content.classList.remove('active'));
        document.getElementById(tabId).classList.add('active');
    });
});

// Slider value display
const confidenceSlider = document.querySelector('input[type="range"]');
const confidenceValue = document.getElementById('confidenceValue');
const executionSlider = document.querySelectorAll('input[type="range"]')[1];
const executionValue = document.getElementById('executionValue');

if (confidenceSlider && confidenceValue) {
    confidenceSlider.addEventListener('input', () => {
        confidenceValue.textContent = confidenceSlider.value;
    });
}

if (executionSlider && executionValue) {
    executionSlider.addEventListener('input', () => {
        executionValue.textContent = executionSlider.value;
    });
}

// File upload preview
const fileUpload = document.querySelector('.file-upload');
const fileInput = document.querySelector('.file-upload input[type="file"]');
const previewContainer = document.getElementById('previewContainer');

if (fileUpload && fileInput && previewContainer) {
    fileUpload.addEventListener('click', () => {
        fileInput.click();
    });

    fileInput.addEventListener('change', (e) => {
        previewContainer.innerHTML = '';

        Array.from(e.target.files).forEach(file => {
            if (!file.type.match('image.*')) return;

            const reader = new FileReader();
            reader.onload = (event) => {
                const preview = document.createElement('div');
                preview.className = 'relative group';
                preview.innerHTML = `
                            <div class="screenshot-thumbnail rounded-lg overflow-hidden h-32">
                                <img src="${event.target.result}" class="w-full h-full object-cover">
                            </div>
                            <button class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                                <i class="fas fa-times"></i>
                            </button>
                        `;
                preview.querySelector('button').addEventListener('click', () => {
                    preview.remove();
                });
                previewContainer.appendChild(preview);
            };
            reader.readAsDataURL(file);
        });
    });
}

// Enhanced Rules Selection
const ruleSearch = document.getElementById('ruleSearch');
const ruleDropdown = document.getElementById('ruleDropdown');
const selectedRules = document.getElementById('selectedRules');

// Track selected rules
const selectedRuleIds = new Set();

if (ruleSearch && ruleDropdown && selectedRules) {
    // Show dropdown when typing
    ruleSearch.addEventListener('focus', () => {
        ruleDropdown.classList.remove('hidden');
    });

    ruleSearch.addEventListener('blur', () => {
        setTimeout(() => ruleDropdown.classList.add('hidden'), 200);
    });

    ruleSearch.addEventListener('input', () => {
        const searchTerm = ruleSearch.value.toLowerCase();
        const items = ruleDropdown.querySelectorAll('div');

        items.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });

    // Add rule when clicked
    ruleDropdown.querySelectorAll('div').forEach(item => {
        item.addEventListener('mousedown', (e) => {
            e.preventDefault();
            const ruleId = item.getAttribute('data-rule');

            if (!selectedRuleIds.has(ruleId)) {
                selectedRuleIds.add(ruleId);
                renderSelectedRules();
            }

            ruleSearch.value = '';
            ruleDropdown.classList.add('hidden');
        });
    });
}

// Render selected rules as chips
function renderSelectedRules() {
    if (!selectedRules) return;

    selectedRules.innerHTML = '';

    // Clear previous hidden inputs
    const hiddenContainer = document.getElementById('selectedRuleInputs');
    hiddenContainer.innerHTML = '';

    selectedRuleIds.forEach(ruleId => {
        // --- Visible Chip ---
        const chip = document.createElement('div');
        chip.className = 'rule-chip';
        chip.innerHTML = `
            ${rules[ruleId]}
            <button class="ml-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300" data-rule="${ruleId}">
                <i class="fas fa-times text-xs"></i>
            </button>
        `;
        selectedRules.appendChild(chip);

        // --- Hidden Input ---
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'selected_rules[]';
        hiddenInput.value = ruleId;
        hiddenContainer.appendChild(hiddenInput);

        // --- Remove chip + input ---
        chip.querySelector('button').addEventListener('click', (e) => {
            e.stopPropagation();
            selectedRuleIds.delete(ruleId);
            renderSelectedRules();
        });
    });
}


// Screenshot Preview Functionality
const screenshotPreview = document.querySelector('.screenshot-preview');
const previewImage = document.getElementById('preview-image');
const closePreview = document.querySelector('.close-preview');

function setupScreenshotPreview() {
    const thumbnails = document.querySelectorAll('#modal-screenshots img');

    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', () => {
            const fullSizeSrc = thumbnail.getAttribute('data-src');
            previewImage.src = fullSizeSrc;
            screenshotPreview.classList.add('active');
        });
    });
}

if (closePreview) {
    closePreview.addEventListener('click', () => {
        screenshotPreview.classList.remove('active');
    });
}

if (screenshotPreview) {
    screenshotPreview.addEventListener('click', (e) => {
        if (e.target === screenshotPreview) {
            screenshotPreview.classList.remove('active');
        }
    });
}

let debounceTimer;

function calculateAmount() {
    const quantity = parseFloat($('#entry_quantity').val()) || 0;
    const price = parseFloat($('#entry_price').val()) || 0;
    const amount = quantity * price;

    $('#entry_amount').val(amount.toFixed(2));
}

function debounceCalculate() {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(calculateAmount, 500); // 500ms delay
}

// Attach to input events
$('#entry_quantity, #entry_price').on('input', debounceCalculate);

let debouncePnLTimer;

function calculatePnL() {
    const entryPrice = parseFloat($('#entry_price').val()) || 0;
    const exitPrice = parseFloat($('#exit_price').val()) || 0;
    const quantity = parseFloat($('#entry_quantity').val()) || 0;
    const tradeType = parseInt($('#trade_type').val()); // 1 = Long, 2 = Short

    let pnlAmount = 0;
    let pnlPercent = 0;

    if (entryPrice > 0 && quantity > 0) {
        if (tradeType === 1) {
            // Long: Buy low, sell high
            pnlAmount = (exitPrice - entryPrice) * quantity;
            pnlPercent = ((exitPrice - entryPrice) / entryPrice) * 100;
        } else if (tradeType === 2) {
            // Short: Sell high, buy low
            pnlAmount = (entryPrice - exitPrice) * quantity;
            pnlPercent = ((entryPrice - exitPrice) / entryPrice) * 100;
        }
    }

    $('#pnlAmount').val(pnlAmount.toFixed(2));
    $('#pnlPercent').val(pnlPercent.toFixed(2));
}

function debounceCalculatePnL() {
    clearTimeout(debouncePnLTimer);
    debouncePnLTimer = setTimeout(calculatePnL, 500); // 500ms delay
}

$(document).ready(function () {
    $('#entry_price, #exit_price, #entry_quantity').on('input', debounceCalculatePnL);

    $('#btn-long').click(function () {
        // Make "Long" active
        $(this)
            .removeClass('border-gray-300 bg-gray-50 text-gray-700 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300')
            .addClass('border-green-500 bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-300');

        // Make "Short" inactive
        $('#btn-short')
            .removeClass('border-red-500 bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-300')
            .addClass('border-gray-300 bg-gray-50 text-gray-700 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300');

        $('#trade_type').val(1)
        debounceCalculatePnL()
    });

    $('#btn-short').click(function () {
        // Make "Short" active
        $(this)
            .removeClass('border-gray-300 bg-gray-50 text-gray-700 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300')
            .addClass('border-red-500 bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-300');

        // Make "Long" inactive
        $('#btn-long')
            .removeClass('border-green-500 bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-300')
            .addClass('border-gray-300 bg-gray-50 text-gray-700 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300');

        $('#trade_type').val(2)
        debounceCalculatePnL()
    });
});

function saveTrade(postUrl) {

    var isValid = true;
    var inputname = "";

    $('.tReq').each(function () {
        var value = $(this).val();
        if (!value) {
            inputname = $(this).data('inputname');
            isValid = false;
            return false;
        }
    });

    if (!isValid) {
        createToast('error', 'Validation Error', `${inputname} is required!`);
        return false;
    }

    // if ($('input[name="selected_rules[]"]').length === 0) {
    //     createToast('error', 'Validation Error', `Please select at least one rule.`);
    //     return false;
    // }

    const form = $('#tradeForm')[0];
    const formData = new FormData(form);

    // Disable the button
    $('#saveTradeBtn').prop('disabled', true).text('Saving...');

    $.ajax({
        url: postUrl,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function (response) {
            if (response.status) {
                successModal.style.display = 'flex';
                $('#response_message').text(response.response_message);
                $('#todays_pnl').text(response.today_pnl)
                    .removeClass()
                    .addClass(`font-medium text-${response.today_color}-600`);
                $('#yesterdays_pnl').text(response.yesterday_pnl)
                    .removeClass()
                    .addClass(`font-medium text-${response.yesterday_color}-600`);
                $('#comparison_message').text(response.message);
                $('#message_icon').removeClass().addClass(`${response.message_icon} text-blue-500 mr-2`);

                getDashboardMetrics();
                loadTrades();
                getEquityChartData();
                fetchTopTrades();
            } else {
                createToast('error', 'Error', 'Form submission failed.');
            }
        },
        error: function () {
            createToast('error', 'Server Error', 'An error occurred.');
        },
        complete: function () {
            // Re-enable the button
            $('#saveTradeBtn').prop('disabled', false).text('Save Trade');
        }
    });
}


// RENDER TABLE
let currentPage = 1;

function loadTrades(page = currentPage) {
    currentPage = page;

    let startDate = $('#startDate').val() || '';
    let endDate = $('#endDate').val() || '';

    let selectedStrategies = $('.filter-card input[type="checkbox"]:checked')
        .map(function () {
            return $(this).val();
        })
        .get()
        .join(',');

    // Handle direction = 0 as both 1 and 2 (null or blank means both)
    let direction = $('input[name="direction"]:checked').val();
    direction = direction === '' ? '0' : direction;
    
    let market_type = $('input[name="mkt_type"]:checked').val();
    market_type = market_type === '' ? '0' : market_type;

    let sortBy = $('#sortSelect').val()

    $.ajax({
        url: base_url + '/ajax-trades',
        type: 'GET',
        dataType: 'json',
        data: { page: page, perPage: 3, startDate, endDate, selectedStrategies, direction, sortBy, market_type },
        success: function (res) {
            renderTable(res.data);
            renderPagination(res.currentPage, res.totalPages);
            updateSummary(res.currentPage, res.perPage, res.total);
        }
    });
}

function renderTable(data) {
    const tbody = $('.trade-table-container tbody');
    tbody.empty();

    if (data.length === 0) {
        tbody.append('<tr><td colspan="8" class="text-center">No trades found</td></tr>');
        return;
    }

    data.forEach(trade => {
        const direction = trade.trade_type === '1' ? 'Long' : 'Short';
        const directionClass = direction === 'Long'
            ? 'dark:bg-green-800 bg-green-600 text-green-100'
            : 'dark:bg-red-800 bg-red-600 text-red-100';

        const entry = parseFloat(trade.entry_price);
        const stop = parseFloat(trade.stop_loss);
        const exit = parseFloat(trade.exit_price);

        const risk = Math.abs(entry - stop);

        const plAmount = parseFloat(trade.pnl_amount);
        const plPercent = parseFloat(trade.pnl_percent);

        // --- RR Ratio from response ---
        let rrRatio = 'N/A';
        let rrClass = 'rr-ratio rr-poor'; // Default to red

        if (typeof trade.rr_ratio !== 'undefined' && trade.rr_ratio !== null) {
            const ratio = parseFloat(trade.rr_ratio);
            const raw = ratio.toFixed(2).replace(/\.00$/, '');
            rrRatio = `1:${raw}`;

            if (ratio <= 1.0) {
                rrClass = 'rr-ratio rr-poor'; // Red
            } else if (ratio > 1.0 && ratio <= 1.5) {
                rrClass = 'rr-ratio rr-orange'; // Orange
            } else if (ratio > 1.5 && ratio <= 2.0) {
                rrClass = 'rr-ratio rr-medium'; // Yellow
            } else {
                rrClass = 'rr-ratio rr-good'; // Green
            }
        }

        const formatMoney = value => {
            const num = parseFloat(value);
            return '' + num.toLocaleString('en-IN', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            }).replace(/\.00$/, '');
        };

        const formatPercent = value => {
            const num = parseFloat(value);
            return num.toFixed(2).replace(/\.00$/, '') + '%';
        };

        tbody.append(`
        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer transition-colors ${!trade.strategy || !trade.summary ? 'dark:bg-orange-500/5 bg-orange-500/10' : ''}"
    onclick="viewTrade('${trade.id}', '${rrRatio}')">

            <td class="px-6 py-4 text-sm whitespace-nowrap">${formatDate(trade.datetime)}</td>
            <td class="px-6 py-4 text-sm font-medium">${trade.symbol}</td>
            <td class="px-6 py-4">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${directionClass}">${direction}</span>
            </td>
            <td class="px-6 py-4 text-sm">
                <div>${formatMoney(entry)}</div>
                <div>${formatMoney(exit)}</div>
            </td>
            <td class="px-6 py-4">
                <div class="text-sm font-medium ${plAmount >= 0 ? 'text-green-500' : 'text-red-500'}">
                    ${plAmount >= 0 ? '+' : ''}${formatMoney(plAmount)}
                </div>
                <div class="text-xs ${plPercent >= 0 ? 'text-green-500' : 'text-red-500'}">
                    ${plPercent >= 0 ? '+' : ''}${formatPercent(plPercent)}
                </div>
            </td>
            <td class="px-6 py-4 text-sm text-center">
                <span class="${rrClass}">
                    ${rrRatio}
                </span>
            </td>
            <td class="px-6 py-4 text-sm text-center whitespace-nowrap">
                ${trade.strategy ? trade.strategy : '—'}
            </td>
            <td class="px-6 py-4 text-center text-sm">
                ${trade.summary && trade.color
                ? `<span class="px-2 py-1 text-xs font-medium rounded-full dark:bg-${trade.color}-800 bg-${trade.color}-600 text-${trade.color}-100 whitespace-nowrap">
                            ${trade.summary}
                       </span>`
                : '—'
            }
            </td>
            <td class="px-6 py-4 text-right">
                <button class="text-blue-600" onclick="event.stopPropagation(); getEditTradeData(${trade.id})">
                    <i class="fas ${!trade.strategy || !trade.summary ? 'fa-book-open' : 'fa-edit'}"></i>
                </button>

                <button class="text-red-600" onclick="event.stopPropagation(); confirmDelete(${trade.id})"><i class="fas fa-trash"></i></button>
            </td>
        </tr>
    `);
    });
}


function renderPagination(current, total) {
    const pagination = $('.pagination-container');
    pagination.empty();

    let buttons = '';

    // Prev
    buttons += `<button class="page-btn px-3 py-1 border rounded-lg ${current === 1 ? 'opacity-50 cursor-not-allowed' : ''}" data-page="${current - 1}" ${current === 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i>
        </button>`;

    // Page numbers
    for (let i = 1; i <= total; i++) {
        buttons += `<button class="page-btn px-3 py-1 border rounded-lg ${i === current ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-200'}" data-page="${i}">
                ${i}
            </button>`;
    }

    // Next
    buttons += `<button class="page-btn px-3 py-1 border rounded-lg ${current === total ? 'opacity-50 cursor-not-allowed' : ''}" data-page="${current + 1}" ${current === total ? 'disabled' : ''}>
            <i class="fas fa-chevron-right"></i>
        </button>`;

    pagination.append(buttons);
}

function updateSummary(currentPage, perPage, total) {
    const from = (currentPage - 1) * perPage + 1;
    const to = Math.min(currentPage * perPage, total);
    $('.pagination-summary').html(`
            Showing <span class="font-medium">${from}</span> to 
            <span class="font-medium">${to}</span> of 
            <span class="font-medium">${total}</span> trades
        `);
}

function formatDate(datetimeStr) {
    const date = new Date(datetimeStr);
    return date.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        // hour: '2-digit',
        // minute: '2-digit',
    });
}

$(document).on('click', '.pagination-container .page-btn', function () {
    const page = $(this).data('page');
    if (!$(this).prop('disabled')) {
        loadTrades(page);
    }
});

function confirmDelete(id) {
    showConfirmDialog({
        message: `Are you sure you want to delete this trade? This action cannot be undone.`,
        tradeId: id,
        action: 'delete',
        callback: deleteTrade
    });
}

function deleteTrade(id) {
    $.ajax({
        type: "POST",
        url: base_url + "deleteTrade",
        data: { id },
        dataType: "JSON",
        success: function (response) {
            if (response.status) {
                createToast('success', 'Success', response.message);
                loadTrades(); // Reload trades after successful deletion
            } else {
                createToast('error', 'Error', response.message || 'Failed to delete trade.');
            }
        },
        error: function (xhr, status, error) {
            // This handles network errors or unexpected server responses
            createToast('error', 'Error', 'An unexpected error occurred. Please try again.');
            console.error('AJAX Error:', status, error);
        }
    });
}

// view trade modal code
const viewTradeDetailsModal = document.getElementById('viewTradeDetailsModal');
const closeModalBtnDet = document.getElementById('closeModalDet');
const closeTradeViewButton = document.getElementById('closeTradeViewButton');
const tabButtonsDet = document.querySelectorAll('.tab-btn-det');
const tabContentsDet = document.querySelectorAll('.tab-content-det');
const emojiSelectors = document.querySelectorAll('.emoji-selector');

function viewTrade(id, rrRatio) {

    $.ajax({
        type: "POST",
        url: base_url + "viewTrade",
        data: { id },
        dataType: "JSON",
        success: function (response) {
            viewTradeDetailsModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
            $('[data-tab="general"]').trigger('click');

            const trade = response.trade || {};

            const entry = parseFloat(trade.entry_price) || 0;
            const stop = parseFloat(trade.stop_loss) || 0;
            const target = parseFloat(trade.target) || 0;
            const direction = trade.trade_type === '1' ? 'Long' : 'Short';

            let riskPercent = 0;
            let rewardPercent = 0;

            if (entry > 0) {
                if (direction === 'Long') {
                    riskPercent = ((entry - stop) / entry) * 100;
                    rewardPercent = ((target - entry) / entry) * 100;
                } else {
                    riskPercent = ((stop - entry) / entry) * 100;
                    rewardPercent = ((entry - target) / entry) * 100;
                }
            }

            riskPercent = isNaN(riskPercent) ? '-' : riskPercent.toFixed(2).replace(/\.00$/, '');
            rewardPercent = isNaN(rewardPercent) ? '-' : rewardPercent.toFixed(2).replace(/\.00$/, '');
            const riskRewardString = `${riskPercent}% / ${rewardPercent}%`;

            const expectedRR = formatRRR(entry, stop, target, trade.trade_type);

            $('#viewSymbol').text(trade.symbol || '—');
            $('#viewStrategy, #viewStrategyBadge').text(trade.strategy || '—');
            $('#viewRrRatio').text(expectedRR || '—');
            $('#viewActualRR').text(rrRatio || '—');
            $('#viewOutcome').text(trade.summary || '—');
            $('#viewPositionSize').text(`${trade.entry_quantity || 0} Qty`);
            $('#viewRiskPercent').text(riskRewardString);

            $('#viewEntryPrice').text(formatCash(trade.entry_price || 0));
            $('#viewExitPrice').text(formatCash(trade.exit_price || 0));
            $('#viewStopLoss').text(formatCash(trade.stop_loss || 0));
            $('#viewTarget').text(formatCash(trade.target || 0));
            $('#viewPnl').text(formatCash(trade.pnl_amount || 0));

            const directionClass = direction === 'Long' ? 'text-green-500' : 'text-red-500';

            $('#viewDirection')
                .text(direction)
                .removeClass('text-green-500 text-red-500')
                .addClass(directionClass);

            const pnlAmount = parseFloat(trade.pnl_amount) || 0;
            const pnlClass = pnlAmount >= 0 ? 'text-green-500' : 'text-red-500';

            $('#viewPnl')
                .text(formatCash(pnlAmount))
                .removeClass('text-green-500 text-red-500')
                .addClass(pnlClass);

            $('#viewPnlPercent')
                .text(`${trade.pnl_percent || '0'}%`)
                .removeClass('text-green-500 text-red-500')
                .addClass(pnlClass);

            const confidence = parseFloat(trade.confidence);
            $('#viewConfidence').css('width', `${isNaN(confidence) ? 0 : confidence * 10}%`);
            $('#viewConfidenceTExt').text(isNaN(confidence) ? '0' : confidence);

            const satisfaction = parseFloat(trade.satisfaction);
            $('#viewSatisfaction').css('width', `${isNaN(satisfaction) ? 0 : satisfaction * 10}%`);
            $('#viewSatisfactionText').text(isNaN(satisfaction) ? '0' : satisfaction);

            $('#viewEmotion').text(trade.emotion || '—');
            $('#viewRationale').text(trade.rationale || '—');
            $('#viewLesson').text(trade.lesson || '—');

            const mistakesContainer = $('#viewMistakesContainer');
            mistakesContainer.empty();

            if (!response.mistakes || response.mistakes.length === 0) {
                mistakesContainer.append(`
                    <div class="bg-white dark:bg-gray-600 rounded-lg overflow-hidden mb-2">
                        <input type="checkbox" id="mistake_nm" class="hidden">
                        <label for="mistake_nm" class="flex items-center justify-between p-3 cursor-pointer">
                            <div class="flex items-center">
                                <div class="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-3">
                                    <i class="fas fa-check text-green-500 text-xs"></i>
                                </div>
                                <span class="font-medium text-gray-800 dark:text-gray-200">No mistakes recorded for this trade.</span>
                            </div>
                        </label>
                        <div class="dropdown-content"><div class="px-4 pb-3 text-sm text-gray-600 dark:text-gray-300">...</div></div>
                    </div>
                `);
            } else {
                $.each(response.mistakes, function (m, mistake) {
                    mistakesContainer.append(`
                        <div class="bg-white dark:bg-gray-600 rounded-lg overflow-hidden mb-2">
                            <input type="checkbox" id="mistake_${m}" class="hidden">
                            <label for="mistake_${m}" class="flex items-center justify-between p-3 cursor-pointer">
                                <div class="flex items-center">
                                    <div class="w-5 h-5 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center mr-3">
                                        <i class="fas fa-times text-red-500 text-xs"></i>
                                    </div>
                                    <span class="font-medium text-gray-800 dark:text-gray-200">${mistake.mistake || 'Unknown Mistake'}</span>
                                </div>
                            </label>
                            <div class="dropdown-content"><div class="px-4 pb-3 text-sm text-gray-600 dark:text-gray-300">...</div></div>
                        </div>
                    `);
                });
            }

            const viewImgContainer = $('#viewImgContainer');
            viewImgContainer.empty();

            if (!response.screenshots || response.screenshots.length === 0) {
                viewImgContainer.append(`
                    <div class="flex flex-col justify-center items-center w-full py-16 text-center text-gray-500 dark:text-gray-400 col-span-full">
                        <i class="fas fa-camera-retro text-6xl mb-4 text-gray-300 dark:text-gray-600"></i>
                        <p class="text-lg font-semibold">No screenshots available</p>
                        <p class="text-sm mt-1 text-gray-400">Nothing captured for this trade yet. Maybe next time!</p>
                    </div>
                `);
            } else {
                $.each(response.screenshots, function (s, scr) {
                    const filePath = scr.file_path ? `${base_url}writable/${scr.file_path}` : '';
                    viewImgContainer.append(`
                        <div class="relative group overflow-hidden rounded-xl shadow-md hover:shadow-lg transition-shadow">
                            <a href="${filePath}" class="glightbox" data-glightbox="title: Screenshot ${s + 1}">
                                <img src="${filePath}" alt="Screenshot ${s + 1}" class="w-full h-32 object-cover rounded-xl group-hover:scale-105 transition-transform duration-200" />
                                <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center text-white text-sm font-medium">
                                    View Screenshot
                                </div>
                            </a>
                        </div>
                    `);
                });
            }

            GLightbox({
                selector: '.glightbox',
                touchNavigation: true,
                loop: true,
                zoomable: true,
                openEffect: 'fade',
                closeEffect: 'fade',
                slideEffect: 'slide',
                autoplayVideos: false
            });
        }
    });
}


// Lightbox functionality
$('#viewImgContainer').on('click', 'img', function () {
    var src = $(this).data('full');
    $('#lightboxImage').attr('src', src);
    $('#imgLightbox').removeClass('hidden');
});

$('#closeLightbox, #imgLightbox').on('click', function (e) {
    // Close only if clicked on overlay or close button
    if (e.target.id === 'imgLightbox' || e.target.id === 'closeLightbox') {
        $('#imgLightbox').addClass('hidden');
        $('#lightboxImage').attr('src', '');
    }
});

function formatRRR(entry, stop, target, tradeType) {
    entry = parseFloat(entry);
    stop = parseFloat(stop);
    target = parseFloat(target);

    if (isNaN(entry) || isNaN(stop) || isNaN(target)) return 'N/A';

    const risk = Math.abs(entry - stop);
    let reward = 0;

    if (tradeType === '1') {
        // Long trade: profit only if target > entry
        reward = Math.max(0, target - entry);
    } else {
        // Short trade: profit only if target < entry
        reward = Math.max(0, entry - target);
    }

    if (risk === 0) return 'N/A';

    const ratio = reward / risk;
    const formatted = ratio.toLocaleString('en-IN', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).replace(/\.00$/, '');

    return `1:${formatted}`;
}

function formatCash(value) {
    const num = parseFloat(value);
    if (isNaN(num)) return '0';
    return '' + num.toLocaleString('en-IN', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).replace(/\.00$/, ''); // remove .00 if unnecessary
}

function formatPnl(value) {
    const num = parseFloat(value);
    if (isNaN(num)) return '0';

    const absFormatted = Math.abs(num).toLocaleString('en-IN', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).replace(/\.00$/, '');

    return absFormatted;
    // (num < 0 ? '-₹ ' : '+₹ ') + 
}


// Close Modal
closeModalBtnDet.addEventListener('click', () => {
    viewTradeDetailsModal.classList.add('hidden');
    document.body.style.overflow = '';
});

closeTradeViewButton.addEventListener('click', () => {
    viewTradeDetailsModal.classList.add('hidden');
    document.body.style.overflow = '';
});

// Close modal when clicking outside
viewTradeDetailsModal.addEventListener('click', (e) => {
    if (e.target === viewTradeDetailsModal) {
        viewTradeDetailsModal.classList.add('hidden');
        document.body.style.overflow = '';
    }
});

// Tab Switching
tabButtonsDet.forEach(button => {
    button.addEventListener('click', () => {
        const tabId = button.getAttribute('data-tab');

        // Update active tab button
        tabButtonsDet.forEach(btn => {
            btn.classList.remove('text-primary-light', 'dark:text-primary-dark', 'border-primary-light', 'dark:border-primary-dark');
            btn.classList.add('text-gray-500', 'dark:text-gray-400', 'border-transparent');
        });
        button.classList.add('text-primary-light', 'dark:text-primary-dark', 'border-primary-light', 'dark:border-primary-dark');
        button.classList.remove('text-gray-500', 'dark:text-gray-400', 'border-transparent');

        // Update active tab content
        tabContents.forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabId).classList.add('active');
    });
});

// Dropdown functionality
document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
    toggle.addEventListener('change', function () {
        const content = this.nextElementSibling.nextElementSibling;
        const arrow = this.nextElementSibling.querySelector('.dropdown-arrow');

        if (this.checked) {
            arrow.classList.add('rotate-180');
        } else {
            arrow.classList.remove('rotate-180');
        }
    });
});

// Emoji selector functionality
emojiSelectors.forEach(selector => {
    selector.addEventListener('click', function () {
        emojiSelectors.forEach(el => el.classList.remove('active'));
        this.classList.add('active');
    });
});

// Simulate loading data
setTimeout(() => {
    document.querySelectorAll('.animate-pulse').forEach(el => {
        el.classList.remove('animate-pulse');
    });
}, 1500);


// Time slot selection
document.querySelectorAll('.time-slot').forEach(slot => {
    slot.addEventListener('click', () => {
        document.querySelectorAll('.time-slot').forEach(s =>
            s.classList.remove('selected', 'text-black', 'text-white', 'bg-blue-500', 'dark:bg-blue-500', 'dark:text-white')
        );
        slot.classList.add('selected', 'text-white', 'dark:text-white', 'bg-blue-500', 'dark:bg-blue-500');
        document.getElementById('selectedTimeSlot').value = slot.dataset.time;
    });
});



// edit
function getEditTradeData(id) {
    $.ajax({
        type: "POST",
        url: base_url + "getEditTradeData",
        data: { id },
        dataType: "JSON",
        success: function (response) {
            addTradeModal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            $('#trade-tab').trigger('click')

            $('#editId').val(id);

            $('#symbol').val(response.trade.symbol)
            $('#market_type').val(response.trade.market_type)
            $('#datetime').val(response.trade.datetime)
            $('#entry_price').val(response.trade.entry_price)
            $('#entry_quantity').val(response.trade.entry_quantity)
            $('#entry_amount').val(response.trade.entry_amount)
            $('#exit_price').val(response.trade.exit_price)
            $('#pnlAmount').val(response.trade.pnl_amount)
            $('#pnlPercent').val(response.trade.pnl_percent)
            $('#stop_loss').val(response.trade.stop_loss)
            $('#target').val(response.trade.target)

            if (response.trade.trade_type == 1) {
                $('#btn-long').trigger('click');
            }
            else {
                $('#btn-short').trigger('click');
            }

            $('#strategy').val(response.trade.strategy)
            $('#outcome').val(response.trade.outcome)
            $('#rationale').val(response.trade.rationale)

            selectedRuleIds.clear();
            response.rules.forEach(r => {
                selectedRuleIds.add(r.rule_id);
            });
            renderSelectedRules();

            $('#confidence').val(response.trade.confidence)
            $('#confidenceValue').text(response.trade.confidence)

            $('#satisfaction').val(response.trade.satisfaction)
            $('#executionValue').text(response.trade.satisfaction)

            $('#emotion').val(response.trade.emotion)

            $('input[name="mistakes[]"]').prop('checked', false);

            response.mistakes.forEach(m => {
                $(`#mistake${m.mistake_id}`).prop('checked', true);
            });

            $('#lesson').val(response.trade.lesson)

            //             var screenshotPreviewContainer = $('#previewContainer');

            //             $.each(response.screenshots, function (s, scr) {
            //                 screenshotPreviewContainer.append(`
            //     <div class="relative group inline-block mr-2 mb-2 w-32 h-32">
            //         <div class="screenshot-thumbnail rounded-lg overflow-hidden w-full h-full">
            //             <img src="${base_url}public/${scr.file_path}" class="w-full h-full object-cover">
            //         </div>
            //         <button type="button" class="absolute top-1 right-1 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity" onclick="removeScreenshot(this, '${scr.file_path}')">
            //             <i class="fas fa-times"></i>
            //         </button>
            //     </div>
            // `);
            //             })

            sliders.forEach(slider => {
                updateSliderBackground(slider);
                slider.addEventListener('input', function () {
                    updateSliderBackground(this);
                });
            });
        }
    });
}

function getDashboardMetrics() {
    let rangeFilter = $('#rangeFilter').val()
    let marketTypeFilter = $('#marketTypeFilter').val()
    $.ajax({
        type: "POST",
        url: base_url + "getDashboardMetrics",
        data: { rangeFilter, marketTypeFilter },
        dataType: "JSON",
        success: function (response) {
            // ✅ Helper: % change text + red/green color
            function setPercentChange(id, value) {
                const el = $(`#${id}`);
                const isPositive = value >= 0;
                const colorClass = isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
                el.text((value > 0 ? '+' : '') + value + '%');
                el.removeClass('text-green-600 text-red-600 dark:text-green-400 dark:text-red-400');
                el.addClass(colorClass);
            }

            // ✅ Helper: non-% change + red/green color
            function setNumericChange(id, value) {
                const el = $(`#${id}`);
                const isPositive = value >= 0;
                const colorClass = isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
                el.text((value > 0 ? '+' : '') + value);
                el.removeClass('text-green-600 text-red-600 dark:text-green-400 dark:text-red-400');
                el.addClass(colorClass);
            }

            // ✅ Helper: capped progress (0% if negative, else max 100%)
            function setProgress(id, value) {
                const width = value < 0 ? 0 : Math.min(value, 100);
                $(`#${id}`).css('width', `${width}%`);
            }

            let cur = 'INR';
            if (currencySymbol == '₹') {
                cur = 'INR';
            }
            else{
                cur = 'USD';
            }

            // ✅ Highest PnL (value coloring based on ±)
            const pnlVal = response.highestPnl.value;
            const formattedPnl = pnlVal.toLocaleString('en-IN', { style: 'currency', currency: cur });

            const pnlColor = pnlVal >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
            const pnlEl = $('#thisMonthHighestPnl');

            pnlEl.text(formattedPnl);
            pnlEl.removeClass('text-green-600 text-red-600 dark:text-green-400 dark:text-red-400').addClass(pnlColor);


            setPercentChange('monthChangePnl', response.highestPnl.change);
            setProgress('monthPnlProgress', response.highestPnl.change);

            // ✅ Win Rate
            $('#thisMonthWinRate').text(`${response.winRate.value}%`);
            setPercentChange('monthChangeWinRate', response.winRate.change);
            setProgress('monthWinRateProgress', response.winRate.change);

            // ✅ Risk/Reward
            $('#thisMonthRiskReward').text(`${response.riskReward.value}`);
            setPercentChange('monthChangeRR', response.riskReward.change);
            setProgress('monthRRProgress', response.riskReward.change);

            // ✅ Trades (no %)
            $('#tradesThisMonth').text(`${response.tradesThisMonth.value}`);
            setNumericChange('monthChangeTrades', response.tradesThisMonth.change);
            setProgress('monthTradeProgress', response.tradesThisMonth.change);

            $('.vsClass').text(response.message)
        }
    });
}

let equityChart;

function getEquityChartData() {
    let marketTypeFilter = $('#marketTypeFilter').val();
    $.post(base_url + "getEquityChartData", { pnlChartFilter, marketTypeFilter }, function (res) {
        if (equityChart) equityChart.destroy();

        const $chartContainer = $('#equityChart').parent();

        // If no data, show message and hide canvas
        if (!res.data || res.data.length === 0) {
            $('#equityChart').hide();
            if ($('#noDataMsg').length === 0) {
                $chartContainer.append(`
                    <div id="noDataMsg" class="flex flex-col justify-center items-center h-full py-16 text-center text-gray-500 dark:text-gray-400">
                        <i class="fas fa-chart-line text-6xl mb-4 text-gray-300 dark:text-gray-600"></i>
                        <p class="text-lg font-semibold">No equity data yet</p>
                        <p class="text-sm mt-1 text-gray-400">Your performance graph starts once you begin logging trades.<br>Let the journey begin!</p>
                    </div>
                `);
            }
            return;
        }

        // Show chart and remove no-data message
        $('#equityChart').show();
        $('#noDataMsg').remove();

        const labels = res.labels.map(label => {
            if (pnlChartFilter === 'D') {
                return new Date(label).toLocaleDateString('en-GB', {
                    day: '2-digit', month: 'short'
                });
            }
            if (pnlChartFilter === 'M') {
                const [month, year] = label.split('-');
                return new Date(`${year}-${month}-01`).toLocaleDateString('en-GB', {
                    month: 'short', year: 'numeric'
                });
            }
            return label;
        });

        const isDark = document.documentElement.classList.contains('dark');

        equityChart = new Chart(document.getElementById('equityChart').getContext('2d'), {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'P&L',
                    data: res.data,
                    borderColor: isDark ? '#3b82f6' : '#93c5fd',
                    backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(147, 197, 253, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: isDark ? '#0f0f13' : '#3b82f6',
                    pointBorderWidth: 2,
                    pointRadius: 4
                }]
            },
            options: chartOptions
        });
    }, "json");
}



$('.pnlChartFilter').click(function () {
    $('.pnlChartFilter').each(function () {
        $(this)
            .removeClass('bg-blue-500 dark:bg-blue-600 text-white')
            .addClass('bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-400 dark:hover:bg-gray-600');
    });

    $(this)
        .removeClass('bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-400 dark:hover:bg-gray-600')
        .addClass('bg-blue-500 dark:bg-blue-600 text-white');

    pnlChartFilter = $(this).text().trim(); // 'D', 'W', or 'M'
    getEquityChartData();
});



// 
const chartOptions = {
    maintainAspectRatio: false,
    plugins: {
        legend: {
            display: false,
            labels: {
                color: '#e0e0e0',
                font: {
                    family: "'Montserrat', sans-serif"
                }
            }
        },
        tooltip: {
            backgroundColor: 'rgba(30, 30, 40, 0.95)',
            titleColor: '#e0e0e0',
            bodyColor: '#e0e0e0',
            borderColor: 'rgba(255, 255, 255, 0.1)',
            borderWidth: 1
        }
    },
    scales: {
        x: {
            grid: {
                color: 'rgba(255, 255, 255, 0.05)'
            },
            ticks: {
                color: '#9ca3af'
            }
        },
        y: {
            grid: {
                color: 'rgba(255, 255, 255, 0.05)'
            },
            ticks: {
                color: '#9ca3af'
            }
        }
    }
};

function fetchTopTrades() {
    let rangeFilter = $('#rangeFilter').val();
    let marketTypeFilter = $('#marketTypeFilter').val();

    $.ajax({
        type: "POST",
        url: base_url + "fetchTopTrades",
        data: { rangeFilter, marketTypeFilter },
        dataType: "JSON",
        success: function (response) {
            let container = $('#topTradesContainer');
            container.empty(); // Clear previous data

            const noDataHtml = `
    <div class="flex flex-col justify-center items-center h-full py-16 text-center text-gray-500 dark:text-gray-400">
    <i class="fas fa-clipboard-list text-6xl mb-4 text-gray-300 dark:text-gray-600"></i>
    <p class="text-lg font-semibold">No trades found</p>
    <p class="text-sm mt-1 text-gray-400">Looks like your journal is waiting for some action.<br>Ready when you are!</p>
</div>
`;

            if (response.length === 0) {
                container.append(noDataHtml);
                return;
            }

            response.forEach(trade => {
                const direction = trade.trade_type == 1 ? 'Long' : 'Short';
                const formattedDate = new Date(trade.datetime).toLocaleDateString('en-IN', {
                    day: '2-digit',
                    month: 'short'
                });

                const infoLine = `${direction} • ${formattedDate}`;

                const pnlColor = trade.pnl_amount >= 0
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-red-600 dark:text-red-400';

                const sign = trade.pnl_amount >= 0 ? '+' : '';
                const pnlAmount = `${sign}${currencySymbol}${parseFloat(trade.pnl_amount).toLocaleString()}`;
                const pnlPercent = `(${parseFloat(trade.pnl_percent).toFixed(2)}%)`;

                const tradeCard = `
                    <div class="trade-card bg-gray-50 dark:bg-gray-700 rounded-lg p-4 transition-all duration-200 cursor-pointer">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="font-medium">${trade.symbol}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">${infoLine}</p>
                            </div>
                            <div class="${pnlColor} font-medium">${pnlAmount} ${pnlPercent}</div>
                        </div>
                        <div class="flex justify-between mt-0 text-sm text-gray-600 dark:text-gray-300">
                            <span>Entry: ${currencySymbol}${parseFloat(trade.entry_price).toFixed(2)}</span>
                            <span>Exit: ${currencySymbol}${parseFloat(trade.exit_price).toFixed(2)}</span>
                        </div>
                    </div>
                `;

                container.append(tradeCard);
            });
        }
    });
}


let mistakesChart;

function getMistakesPieData() {
    let rangeFilter = $('#rangeFilter').val()
    $.post(base_url + "getMistakesPieData", { rangeFilter }, function (res) {
        if (mistakesChart) mistakesChart.destroy();

        const ctx = document.getElementById('mistakesPieChart').getContext('2d');

        mistakesChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: res.labels,
                datasets: [{
                    data: res.data,
                    backgroundColor: [
                        '#1abc9c',
                        '#3498db',
                        '#9b59b6',
                        '#e67e22',
                        '#e74c3c',
                        '#f1c40f',
                        '#2ecc71',
                        '#95a5a6',
                    ],
                    borderWidth: 0,
                    cutout: '70%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(31, 41, 55, 0.9)',
                        padding: 12,
                        titleFont: {
                            size: 12,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 11
                        },
                        callbacks: {
                            label: function (context) {
                                return context.label + ': ' + context.raw + '';
                            }
                        }
                    }
                }
            }
        });
    }, "json");
}

function renderMostCommonMistakes() {
    let rangeFilter = $('#rangeFilter').val()
    let marketTypeFilter = $('#marketTypeFilter').val()
    $.ajax({
        type: 'POST',
        url: base_url + 'getMistakesPieData',
        data: { rangeFilter, marketTypeFilter },
        dataType: 'json',
        success: function (res) {
            const container = $('#mistakeStatsContainer');
            container.empty();

            $('#comMisText').text(res.rangeText);

            if (!res.labels.length) {
                const noDataHtml = `
    <div class="flex flex-col justify-center items-center h-full py-16 text-center text-gray-500 dark:text-gray-400">
        <i class="fas fa-ghost text-6xl mb-4 text-gray-300 dark:text-gray-600"></i>
        <p class="text-lg font-semibold">No mistakes found</p>
        <p class="text-sm mt-1 text-gray-400">You're either perfect or didn't trade much.<br>Keep up the great discipline!</p>
    </div>
`;
                container.append(noDataHtml);
                return;
            }


            res.labels.forEach((mistake, i) => {
                const amount = res.amounts[i];
                const percentage = res.percentages[i];
                const count = res.counts[i];
                const maxCount = res.maxCount || 1;
                const barWidthPercent = (count / maxCount) * 100;
                const colorClass = 'bg-gradient-to-r from-red-600 to-red-400';

                const html = `
            <div class="mb-4">
                <div class="flex justify-between items-center mb-1">
                    <span class="text-sm font-medium">${mistake}</span>
                    <span class="hidden text-sm font-medium text-red-600 dark:text-red-400">-${currencySymbol}${amount.toLocaleString()}</span>
                </div>
                <div class="mistake-bar ${colorClass}" style="width: ${barWidthPercent}%">
                    ${count} trades
                </div>
            </div>
        `;
                container.append(html);
            });
        }
    });
}

function getGradientClass(index) {
    const gradients = [
        'bg-gradient-to-r from-red-500 to-orange-500',
        'bg-gradient-to-r from-red-400 to-orange-400',
        'bg-gradient-to-r from-red-300 to-orange-300',
        'bg-gradient-to-r from-red-200 to-orange-200'
    ];
    return gradients[index % gradients.length];
}






// filters
// Modal toggle functionality
const filtermodal = document.getElementById('modalBackdrop');
document.getElementById('openModal').addEventListener('click', () => {
    filtermodal.classList.remove('hidden');
});

document.getElementById('closeModal').addEventListener('click', () => {
    filtermodal.classList.add('hidden');
});

// Close modal when clicking outside
filtermodal.addEventListener('click', (e) => {
    if (e.target === filtermodal) {
        filtermodal.classList.add('hidden');
    }
});

// Close modal on escape key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && !filtermodal.classList.contains('hidden')) {
        filtermodal.classList.add('hidden');
    }
});

// Add active class to filter cards when clicked
document.querySelectorAll('.filter-card').forEach(card => {
    const input = card.querySelector('input');

    input.addEventListener('change', () => {
        if (input.type === 'checkbox') {
            card.classList.toggle('active', input.checked);
        } else if (input.type === 'radio') {
            // Remove active class from all radio buttons in same name group
            const name = input.name;
            document.querySelectorAll(`input[type="radio"][name="${name}"]`).forEach(radio => {
                radio.closest('.filter-card').classList.remove('active');
            });

            if (input.checked) {
                card.classList.add('active');
            }
        }
    });

    // Optional: Clicking the card triggers input click
    card.addEventListener('click', (e) => {
        if (!e.target.matches('input')) {
            input.click();
        }
    });
});

document.getElementById('resetFilters').addEventListener('click', () => {
    // Reset checkboxes and radio buttons
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.querySelectorAll('input[type="radio"]').forEach(radio => {
        radio.checked = false;
    });

    // Remove active class from filter cards
    document.querySelectorAll('.filter-card').forEach(card => {
        card.classList.remove('active');
    });

    $('#startDate, #endDate').val('');

    loadTrades();
});


function applyFilters() {
    loadTrades();
    filtermodal.classList.add('hidden');
}

$(document).ready(function () {
    $('#sortSelect').change(function () {
        loadTrades();
    })
});


const sliders = document.querySelectorAll("input[type=range]");

function updateSliderBackground(slider) {
    const val = ((slider.value - slider.min) / (slider.max - slider.min)) * 100;
    slider.style.setProperty('--value', val + '%');
}

sliders.forEach(slider => {
    updateSliderBackground(slider);
    slider.addEventListener('input', function () {
        updateSliderBackground(this);
    });
});