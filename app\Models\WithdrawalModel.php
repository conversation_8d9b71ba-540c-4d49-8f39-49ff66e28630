<?php

namespace App\Models;

use CodeIgniter\Model;

class WithdrawalModel extends Model
{
    protected $table = 'withdrawals';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'transaction_id',
        'user_id',
        'amount',
        'status',
        'admin_notes',
        'processed_by',
        'processed_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'user_id' => 'required|integer',
        'amount' => 'required|decimal|greater_than[0]',
        'status' => 'required|in_list[pending,approved,rejected]'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'amount' => [
            'required' => 'Amount is required',
            'decimal' => 'Amount must be a valid decimal',
            'greater_than' => 'Amount must be greater than 0'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be pending, approved, or rejected'
        ]
    ];

    /**
     * Get withdrawal requests with user and bank details
     */
    public function getWithdrawalsWithDetails($filters = [])
    {
        $builder = $this->db->table('withdrawals w')
            ->select('w.*, u.full_name, u.email, u.refer_code, u.wallet_balance, 
                     bd.account_name, bd.bank_name, bd.account_number, bd.ifsc_code, bd.branch_name')
            ->join('users u', 'w.user_id = u.id', 'left')
            ->join('bank_details bd', 'w.user_id = bd.user_id', 'left');

        // Apply filters
        if (!empty($filters['status'])) {
            $builder->where('w.status', $filters['status']);
        }

        if (!empty($filters['search'])) {
            $builder->groupStart()
                ->like('u.full_name', $filters['search'])
                ->orLike('u.email', $filters['search'])
                ->orLike('u.refer_code', $filters['search'])
                ->orLike('w.transaction_id', $filters['search'])
                ->groupEnd();
        }

        if (!empty($filters['bank'])) {
            $builder->like('bd.bank_name', $filters['bank']);
        }

        if (!empty($filters['date_from'])) {
            $builder->where('w.created_at >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $builder->where('w.created_at <=', $filters['date_to']);
        }

        if (!empty($filters['amount_min'])) {
            $builder->where('w.amount >=', $filters['amount_min']);
        }

        if (!empty($filters['amount_max'])) {
            $builder->where('w.amount <=', $filters['amount_max']);
        }

        return $builder->orderBy('w.created_at', 'DESC');
    }

    /**
     * Get withdrawal statistics
     */
    public function getWithdrawalStats()
    {
        $stats = [];

        // Total withdrawals by status
        $statusStats = $this->db->table('withdrawals')
            ->select('status, COUNT(*) as count, SUM(amount) as total_amount')
            ->groupBy('status')
            ->get()
            ->getResultArray();

        foreach ($statusStats as $stat) {
            $stats[$stat['status']] = [
                'count' => $stat['count'],
                'amount' => $stat['total_amount']
            ];
        }

        // Today's withdrawals
        $today = $this->db->table('withdrawals')
            ->where('DATE(created_at)', date('Y-m-d'))
            ->selectSum('amount', 'total_amount')
            ->countAllResults(false);

        $todayAmount = $this->db->table('withdrawals')
            ->where('DATE(created_at)', date('Y-m-d'))
            ->selectSum('amount')
            ->get()
            ->getRowArray();

        $stats['today'] = [
            'count' => $today,
            'amount' => $todayAmount['amount'] ?? 0
        ];

        // This month's withdrawals
        $thisMonth = $this->db->table('withdrawals')
            ->where('YEAR(created_at)', date('Y'))
            ->where('MONTH(created_at)', date('m'))
            ->selectSum('amount', 'total_amount')
            ->countAllResults(false);

        $thisMonthAmount = $this->db->table('withdrawals')
            ->where('YEAR(created_at)', date('Y'))
            ->where('MONTH(created_at)', date('m'))
            ->selectSum('amount')
            ->get()
            ->getRowArray();

        $stats['this_month'] = [
            'count' => $thisMonth,
            'amount' => $thisMonthAmount['amount'] ?? 0
        ];

        return $stats;
    }

    /**
     * Approve withdrawal request
     */
    public function approveWithdrawal($withdrawalId, $adminId, $notes = '')
    {
        $withdrawal = $this->find($withdrawalId);
        
        if (!$withdrawal || $withdrawal['status'] !== 'pending') {
            return ['success' => false, 'message' => 'Invalid withdrawal request or already processed.'];
        }

        $this->db->transStart();

        // Update withdrawal status
        $this->update($withdrawalId, [
            'status' => 'approved',
            'admin_notes' => $notes,
            'processed_by' => $adminId,
            'processed_at' => date('Y-m-d H:i:s')
        ]);

        $this->db->transComplete();

        if ($this->db->transStatus() === false) {
            return ['success' => false, 'message' => 'Failed to approve withdrawal.'];
        }

        return ['success' => true, 'message' => 'Withdrawal approved successfully.'];
    }

    /**
     * Reject withdrawal request
     */
    public function rejectWithdrawal($withdrawalId, $adminId, $notes = '')
    {
        $withdrawal = $this->find($withdrawalId);
        
        if (!$withdrawal || $withdrawal['status'] !== 'pending') {
            return ['success' => false, 'message' => 'Invalid withdrawal request or already processed.'];
        }

        $this->db->transStart();

        // Update withdrawal status
        $this->update($withdrawalId, [
            'status' => 'rejected',
            'admin_notes' => $notes,
            'processed_by' => $adminId,
            'processed_at' => date('Y-m-d H:i:s')
        ]);

        // Refund amount to user's wallet
        $this->db->table('users')
            ->where('id', $withdrawal['user_id'])
            ->set('wallet_balance', 'wallet_balance + ' . $withdrawal['amount'], false)
            ->update();

        $this->db->transComplete();

        if ($this->db->transStatus() === false) {
            return ['success' => false, 'message' => 'Failed to reject withdrawal.'];
        }

        return ['success' => true, 'message' => 'Withdrawal rejected and amount refunded to wallet.'];
    }

    /**
     * Get withdrawal by transaction ID
     */
    public function getByTransactionId($transactionId)
    {
        return $this->where('transaction_id', $transactionId)->first();
    }

    /**
     * Get user's withdrawal history
     */
    public function getUserWithdrawals($userId, $limit = 10, $offset = 0)
    {
        return $this->where('user_id', $userId)
                   ->orderBy('created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get monthly withdrawal report
     */
    public function getMonthlyReport($year, $month)
    {
        return $this->db->table('withdrawals w')
            ->select('w.*, u.full_name, u.email, u.refer_code')
            ->join('users u', 'w.user_id = u.id', 'left')
            ->where('YEAR(w.created_at)', $year)
            ->where('MONTH(w.created_at)', $month)
            ->orderBy('w.created_at', 'DESC')
            ->get()
            ->getResultArray();
    }
}
