<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Admin Panel - Trade Diary' ?></title>
    
    <!-- CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <style>
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        
        .sidebar-overlay {
            background: rgba(0, 0, 0, 0.5);
            transition: opacity 0.3s ease-in-out;
        }
        
        .sidebar-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .sidebar-overlay:not(.hidden) {
            opacity: 1;
        }
        
        .table-hover tbody tr:hover {
            background-color: #f8fafc;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: capitalize;
        }
        
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .status-approved {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .status-rejected {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .admin-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .stats-card {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        
        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            padding: 1rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        
        .toast.show {
            transform: translateX(0);
        }
        
        .toast-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .toast-error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        
        .toast-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        
        .toast-info {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }
        
        /* Dark mode styles */
        .dark {
            color-scheme: dark;
        }

        .dark .bg-gray-50 {
            background-color: #1f2937 !important;
        }

        .dark .bg-white {
            background-color: #374151 !important;
        }

        .dark .text-gray-900 {
            color: #f9fafb !important;
        }

        .dark .text-gray-700 {
            color: #d1d5db !important;
        }

        .dark .text-gray-500 {
            color: #9ca3af !important;
        }

        .dark .text-gray-400 {
            color: #6b7280 !important;
        }

        .dark .border-gray-200 {
            border-color: #4b5563 !important;
        }

        .dark .border-gray-300 {
            border-color: #4b5563 !important;
        }

        .dark .bg-gray-100 {
            background-color: #4b5563 !important;
        }

        .dark .bg-gray-800 {
            background-color: #1f2937 !important;
        }

        .dark .bg-gray-900 {
            background-color: #111827 !important;
        }

        .dark .hover\:bg-gray-100:hover {
            background-color: #374151 !important;
        }

        .dark .hover\:text-gray-700:hover {
            color: #f3f4f6 !important;
        }

        .dark .hover\:bg-gray-50:hover {
            background-color: #374151 !important;
        }

        .dark .shadow-sm {
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.5);
        }

        .dark .shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.6);
        }

        .dark .divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
            border-color: #4b5563;
        }

        /* Dark mode table improvements */
        .dark tbody tr {
            background-color: #1f2937 !important;
            border-color: #374151 !important;
        }

        .dark tbody tr:hover {
            background-color: #374151 !important;
        }

        .dark thead th {
            background-color: #111827 !important;
            color: #d1d5db !important;
            border-color: #374151 !important;
        }

        .dark .table-hover tbody tr:hover {
            background-color: #374151 !important;
        }

        /* Dark mode text improvements */
        .dark .text-indigo-600 {
            color: #818cf8 !important;
        }

        .dark .text-indigo-400 {
            color: #a5b4fc !important;
        }

        .dark .bg-indigo-100 {
            background-color: #312e81 !important;
        }

        .dark .bg-indigo-900 {
            background-color: #1e1b4b !important;
        }

        .dark .text-indigo-600 {
            color: #a5b4fc !important;
        }

        /* Dark mode form elements */
        .dark input, .dark select, .dark textarea {
            background-color: #374151 !important;
            border-color: #4b5563 !important;
            color: #f9fafb !important;
        }

        .dark input:focus, .dark select:focus, .dark textarea:focus {
            border-color: #6366f1 !important;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
        }

        /* Dark mode card improvements */
        .dark .stats-card {
            background-color: #1f2937 !important;
            border-color: #374151 !important;
        }

        /* Dark mode status badges */
        .dark .status-pending {
            background-color: #92400e !important;
            color: #fef3c7 !important;
        }

        .dark .status-approved {
            background-color: #065f46 !important;
            color: #d1fae5 !important;
        }

        .dark .status-rejected {
            background-color: #991b1b !important;
            color: #fee2e2 !important;
        }

        /* Dark mode button improvements */
        .dark .bg-green-600 {
            background-color: #059669 !important;
        }

        .dark .bg-red-600 {
            background-color: #dc2626 !important;
        }

        .dark .bg-indigo-600 {
            background-color: #4f46e5 !important;
        }

        .dark .bg-gray-500 {
            background-color: #6b7280 !important;
        }

        /* Dark mode dropdown improvements */
        .dark .bg-white {
            background-color: #1f2937 !important;
        }

        .dark .border-gray-200 {
            border-color: #374151 !important;
        }

        /* Dark mode notification improvements */
        .dark .bg-red-500 {
            background-color: #ef4444 !important;
        }

        /* Dark mode avatar improvements */
        .dark .bg-indigo-100 {
            background-color: #312e81 !important;
        }

        .dark .text-indigo-600 {
            color: #a5b4fc !important;
        }

        /* Dark mode loading overlay */
        .dark #loadingOverlay .bg-white {
            background-color: #1f2937 !important;
        }

        .dark #loadingOverlay .text-gray-700 {
            color: #d1d5db !important;
        }

        /* Additional dark mode improvements for better contrast */
        .dark .text-gray-600 {
            color: #d1d5db !important;
        }

        .dark .text-gray-800 {
            color: #f3f4f6 !important;
        }

        .dark .bg-gray-50 {
            background-color: #111827 !important;
        }

        .dark .border-b {
            border-color: #374151 !important;
        }

        .dark .border-t {
            border-color: #374151 !important;
        }

        /* Dark mode specific table styling */
        .dark table {
            background-color: #1f2937 !important;
        }

        .dark td {
            border-color: #374151 !important;
        }

        .dark th {
            background-color: #111827 !important;
            border-color: #374151 !important;
        }

        /* Dark mode for inline badges and spans */
        .dark .inline-flex {
            background-color: inherit;
        }

        /* Dark mode for wallet balance and amounts */
        .dark .font-semibold {
            color: #f9fafb !important;
        }

        .dark .font-medium {
            color: #f3f4f6 !important;
        }

        /* Dark mode for copy buttons and icons */
        .dark .copy-btn {
            color: #9ca3af !important;
        }

        .dark .copy-btn:hover {
            color: #d1d5db !important;
        }

        .dark .copy-btn.copied {
            color: #34d399 !important;
        }

        /* Dark mode for transaction IDs and codes */
        .dark .font-mono {
            color: #e5e7eb !important;
        }

        /* Dark mode for date and time displays */
        .dark .text-xs {
            color: #9ca3af !important;
        }

        /* Dark mode for action button containers */
        .dark .space-x-1 > * {
            background-color: inherit;
        }

        /* Dark mode toggle button */
        .theme-toggle {
            position: relative;
            width: 3rem;
            height: 1.5rem;
            background-color: #d1d5db;
            border-radius: 0.75rem;
            cursor: pointer;
            transition: background-color 0.3s ease;
            border: none;
            outline: none;
            display: flex;
            align-items: center;
        }

        .theme-toggle:focus {
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .theme-toggle:hover {
            background-color: #9ca3af;
        }

        .theme-toggle.dark {
            background-color: #6366f1;
        }

        .theme-toggle.dark:hover {
            background-color: #4f46e5;
        }

        .theme-toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 1.25rem;
            height: 1.25rem;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .theme-toggle.dark .theme-toggle-slider {
            transform: translateX(1.5rem);
        }

        .theme-icon {
            font-size: 0.75rem;
            color: #fbbf24;
            transition: color 0.3s ease;
        }

        .theme-toggle.dark .theme-icon {
            color: #1e293b;
        }

        /* Tooltip styles */
        .tooltip {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: 9999;
            pointer-events: none;
        }

        /* Copy button hover effects */
        .copy-btn {
            transition: all 0.2s ease-in-out;
        }

        .copy-btn:hover {
            transform: scale(1.1);
        }

        .copy-btn.copied {
            color: #10b981 !important;
            transform: scale(1.2);
        }

        /* Bank details section styling */
        .bank-detail-row {
            transition: background-color 0.2s ease-in-out;
        }

        .bank-detail-row:hover {
            background-color: rgba(99, 102, 241, 0.05);
        }
    </style>
    
    <!-- Global Variables -->
    <script>
        const BASE_URL = '<?= base_url() ?>';
        const ADMIN_BASE_URL = '<?= base_url('admin') ?>';
        const CSRF_TOKEN = '<?= csrf_token() ?>';
        const CSRF_HASH = '<?= csrf_hash() ?>';

        // Dark Mode Management
        const DarkMode = {
            init() {
                const savedTheme = localStorage.getItem('admin-theme') || 'light';
                this.setTheme(savedTheme);
                this.bindEvents();
                this.applyThemeToElements();
            },

            setTheme(theme) {
                if (theme === 'dark') {
                    document.documentElement.classList.add('dark');
                    document.body.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                    document.body.classList.remove('dark');
                }
                localStorage.setItem('admin-theme', theme);
                this.updateToggleButton(theme);
                this.applyThemeToElements();

                // Show toast notification
                if (typeof showToast === 'function') {
                    showToast(`Switched to ${theme} mode`, 'info', 2000);
                }
            },

            toggleTheme() {
                const isDark = document.documentElement.classList.contains('dark');
                this.setTheme(isDark ? 'light' : 'dark');
            },

            updateToggleButton(theme) {
                const toggle = document.querySelector('.theme-toggle');
                const icon = document.querySelector('.theme-icon');
                if (toggle && icon) {
                    if (theme === 'dark') {
                        toggle.classList.add('dark');
                        icon.className = 'theme-icon fas fa-moon';
                        toggle.title = 'Switch to light mode';
                    } else {
                        toggle.classList.remove('dark');
                        icon.className = 'theme-icon fas fa-sun';
                        toggle.title = 'Switch to dark mode';
                    }
                }
            },

            applyThemeToElements() {
                // Force refresh of dynamic elements
                setTimeout(() => {
                    const isDark = document.documentElement.classList.contains('dark');

                    // Update any dynamically loaded content
                    if (isDark) {
                        document.querySelectorAll('.bg-white').forEach(el => {
                            if (!el.classList.contains('theme-toggle-slider')) {
                                el.style.backgroundColor = '#1f2937';
                            }
                        });

                        document.querySelectorAll('.text-gray-900').forEach(el => {
                            el.style.color = '#f9fafb';
                        });

                        document.querySelectorAll('.text-gray-700').forEach(el => {
                            el.style.color = '#d1d5db';
                        });

                        document.querySelectorAll('.text-gray-500').forEach(el => {
                            el.style.color = '#9ca3af';
                        });
                    } else {
                        // Reset to default styles for light mode
                        document.querySelectorAll('.bg-white').forEach(el => {
                            if (!el.classList.contains('theme-toggle-slider')) {
                                el.style.backgroundColor = '';
                            }
                        });

                        document.querySelectorAll('.text-gray-900, .text-gray-700, .text-gray-500').forEach(el => {
                            el.style.color = '';
                        });
                    }
                }, 100);
            },

            bindEvents() {
                document.addEventListener('DOMContentLoaded', () => {
                    const toggle = document.querySelector('.theme-toggle');
                    if (toggle) {
                        toggle.addEventListener('click', () => this.toggleTheme());
                    }
                });
            }
        };

        // Initialize dark mode
        DarkMode.init();
    </script>
</head>
<body class="bg-gray-50 font-sans antialiased transition-colors duration-300">
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="loading-spinner"></div>
            <span class="text-gray-700">Loading...</span>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Main Layout Container -->
    <div class="flex h-screen bg-gray-50">
        <!-- Sidebar Overlay (Mobile) -->
        <div id="sidebarOverlay" class="sidebar-overlay fixed inset-0 z-40 lg:hidden hidden"></div>
