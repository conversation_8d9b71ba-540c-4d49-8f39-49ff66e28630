// Payout Management JavaScript
$(document).ready(function() {
    let currentPage = 1;
    let currentFilters = {};
    let currentAction = null;
    let currentWithdrawalId = null;

    // Initialize
    loadPayoutRequests();
    initializeEventHandlers();

    function initializeEventHandlers() {
        // Filter handlers
        $('#searchFilter').on('input', debounce(applyFilters, 500));
        $('#statusFilter, #bankFilter').on('change', applyFilters);
        $('#dateFromFilter').on('change', applyFilters);
        
        // Button handlers
        $('#refreshData').on('click', function() {
            currentPage = 1;
            loadPayoutRequests();
        });
        
        $('#exportCSV').on('click', exportToCSV);
        $('#clearFilters').on('click', clearFilters);
        
        // Modal handlers
        $('#closeModal').on('click', closeWithdrawalModal);
        $('#cancelAction, #actionModal').on('click', function(e) {
            if (e.target === this) closeActionModal();
        });
        $('#confirmAction').on('click', executeAction);
        
        // Close modals on escape key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                closeWithdrawalModal();
                closeActionModal();
            }
        });
    }

    function loadPayoutRequests() {
        showLoading();
        
        const params = new URLSearchParams({
            page: currentPage,
            per_page: 10,
            ...currentFilters
        });

        $.ajax({
            url: `${ADMIN_BASE_URL}/get-payout-requests?${params}`,
            method: 'GET',
            success: function(response) {
                hideLoading();
                if (response.success) {
                    renderPayoutTable(response.data);
                    renderPagination(response.pagination);
                    updateRecordCount(response.pagination.total_records);
                } else {
                    showToast('Failed to load payout requests', 'error');
                }
            },
            error: function(xhr) {
                hideLoading();
                if (xhr.status === 401) {
                    window.location.href = '/admin/login';
                } else {
                    showToast('Error loading data. Please try again.', 'error');
                }
            }
        });
    }

    function renderPayoutTable(data) {
        const tbody = $('#payoutTableBody');
        tbody.empty();

        if (data.length === 0) {
            tbody.html(`
                <tr>
                    <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                        <i class="fas fa-inbox text-4xl mb-4 text-gray-300"></i>
                        <p class="text-lg">No payout requests found</p>
                        <p class="text-sm">Try adjusting your filters</p>
                    </td>
                </tr>
            `);
            return;
        }

        data.forEach(withdrawal => {
            const row = createPayoutRow(withdrawal);
            tbody.append(row);
        });
    }

    function createPayoutRow(withdrawal) {
        const statusBadge = getStatusBadge(withdrawal.status);
        const actionButtons = getActionButtons(withdrawal);
        
        return `
            <tr class="hover:bg-gray-50 cursor-pointer" onclick="viewWithdrawalDetails(${withdrawal.id})">
                <td class="px-6 py-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                            <span class="text-indigo-600 font-medium text-sm">
                                ${withdrawal.user.name.charAt(0).toUpperCase()}
                            </span>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-900">${withdrawal.user.name}</div>
                            <div class="text-sm text-gray-500">${withdrawal.user.email}</div>
                            <div class="text-xs text-indigo-600">${withdrawal.user.refer_code}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4">
                    <div class="text-sm text-gray-900">${withdrawal.bank_details.bank_name}</div>
                    <div class="text-sm text-gray-500">${withdrawal.bank_details.account_number}</div>
                    <div class="text-xs text-gray-500">${withdrawal.bank_details.ifsc_code}</div>
                </td>
                <td class="px-6 py-4">
                    <div class="text-sm font-medium text-gray-900">₹${formatAmount(withdrawal.amount)}</div>
                    <div class="text-xs text-gray-500">Wallet: ₹${formatAmount(withdrawal.user.wallet_balance)}</div>
                </td>
                <td class="px-6 py-4">
                    <div class="text-sm text-gray-900">${formatDate(withdrawal.created_at)}</div>
                    <div class="text-xs text-gray-500">${withdrawal.transaction_id}</div>
                </td>
                <td class="px-6 py-4">
                    ${statusBadge}
                    ${withdrawal.processed_at ? `<div class="text-xs text-gray-500 mt-1">Processed: ${formatDate(withdrawal.processed_at)}</div>` : ''}
                </td>
                <td class="px-6 py-4 text-right">
                    <div class="flex justify-end space-x-2" onclick="event.stopPropagation()">
                        ${actionButtons}
                    </div>
                </td>
            </tr>
        `;
    }

    function getStatusBadge(status) {
        const badges = {
            'pending': '<span class="status-badge status-pending"><i class="fas fa-clock mr-1"></i>Pending</span>',
            'approved': '<span class="status-badge status-approved"><i class="fas fa-check mr-1"></i>Approved</span>',
            'rejected': '<span class="status-badge status-rejected"><i class="fas fa-times mr-1"></i>Rejected</span>'
        };
        return badges[status] || status;
    }

    function getActionButtons(withdrawal) {
        if (withdrawal.status === 'pending') {
            return `
                <button onclick="approveWithdrawal(${withdrawal.id})" 
                        class="btn-success px-3 py-1 rounded text-white text-sm hover:opacity-90 transition-opacity"
                        data-tooltip="Approve Request">
                    <i class="fas fa-check"></i>
                </button>
                <button onclick="rejectWithdrawal(${withdrawal.id})" 
                        class="btn-danger px-3 py-1 rounded text-white text-sm hover:opacity-90 transition-opacity"
                        data-tooltip="Reject Request">
                    <i class="fas fa-times"></i>
                </button>
            `;
        } else {
            return `
                <button onclick="viewWithdrawalDetails(${withdrawal.id})" 
                        class="bg-gray-500 hover:bg-gray-600 px-3 py-1 rounded text-white text-sm transition-colors"
                        data-tooltip="View Details">
                    <i class="fas fa-eye"></i>
                </button>
            `;
        }
    }

    function renderPagination(pagination) {
        const container = $('#paginationContainer');
        
        if (pagination.total_pages <= 1) {
            container.hide();
            return;
        }
        
        container.show();
        
        let paginationHtml = `
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing ${((pagination.current_page - 1) * pagination.per_page) + 1} to 
                    ${Math.min(pagination.current_page * pagination.per_page, pagination.total_records)} of 
                    ${pagination.total_records} results
                </div>
                <div class="flex space-x-1">
        `;
        
        // Previous button
        if (pagination.current_page > 1) {
            paginationHtml += `
                <button onclick="changePage(${pagination.current_page - 1})" 
                        class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    Previous
                </button>
            `;
        }
        
        // Page numbers
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === pagination.current_page;
            paginationHtml += `
                <button onclick="changePage(${i})" 
                        class="px-3 py-2 text-sm ${isActive ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'} border border-gray-300 rounded-md">
                    ${i}
                </button>
            `;
        }
        
        // Next button
        if (pagination.current_page < pagination.total_pages) {
            paginationHtml += `
                <button onclick="changePage(${pagination.current_page + 1})" 
                        class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    Next
                </button>
            `;
        }
        
        paginationHtml += '</div></div>';
        container.html(paginationHtml);
    }

    function applyFilters() {
        currentFilters = {
            search: $('#searchFilter').val().trim(),
            status: $('#statusFilter').val(),
            bank: $('#bankFilter').val(),
            date_from: $('#dateFromFilter').val()
        };
        
        // Remove empty filters
        Object.keys(currentFilters).forEach(key => {
            if (!currentFilters[key]) {
                delete currentFilters[key];
            }
        });
        
        currentPage = 1;
        loadPayoutRequests();
    }

    function clearFilters() {
        $('#searchFilter').val('');
        $('#statusFilter').val('');
        $('#bankFilter').val('');
        $('#dateFromFilter').val('');
        currentFilters = {};
        currentPage = 1;
        loadPayoutRequests();
    }

    function updateRecordCount(count) {
        $('#recordCount').text(count.toLocaleString());
    }

    // Global functions for onclick handlers
    window.changePage = function(page) {
        currentPage = page;
        loadPayoutRequests();
    };

    window.viewWithdrawalDetails = function(withdrawalId) {
        showLoading();
        
        $.ajax({
            url: `${ADMIN_BASE_URL}/withdrawal-details/${withdrawalId}`,
            method: 'GET',
            success: function(response) {
                hideLoading();
                if (response.success) {
                    showWithdrawalModal(response.data);
                } else {
                    showToast('Failed to load withdrawal details', 'error');
                }
            },
            error: function() {
                hideLoading();
                showToast('Error loading withdrawal details', 'error');
            }
        });
    };

    window.approveWithdrawal = function(withdrawalId) {
        showActionModal('approve', withdrawalId, {
            title: 'Approve Withdrawal',
            message: 'Are you sure you want to approve this withdrawal request?',
            icon: 'fa-check',
            iconColor: 'bg-green-500',
            buttonColor: 'btn-success',
            showNotes: true,
            notesRequired: false
        });
    };

    window.rejectWithdrawal = function(withdrawalId) {
        showActionModal('reject', withdrawalId, {
            title: 'Reject Withdrawal',
            message: 'Are you sure you want to reject this withdrawal request?',
            icon: 'fa-times',
            iconColor: 'bg-red-500',
            buttonColor: 'btn-danger',
            showNotes: true,
            notesRequired: true
        });
    };

    function showActionModal(action, withdrawalId, config) {
        currentAction = action;
        currentWithdrawalId = withdrawalId;
        
        $('#actionTitle').text(config.title);
        $('#actionMessage').text(config.message);
        $('#actionIcon').removeClass().addClass(`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${config.iconColor}`);
        $('#actionIcon i').removeClass().addClass(`fas ${config.icon} text-xl text-white`);
        $('#confirmAction').removeClass().addClass(`px-4 py-2 text-white rounded-lg transition-colors ${config.buttonColor}`);
        
        if (config.showNotes) {
            $('#notesSection').removeClass('hidden');
            $('#actionNotes').prop('required', config.notesRequired);
            if (config.notesRequired) {
                $('#actionNotes').attr('placeholder', 'Please provide a reason for rejection...');
            } else {
                $('#actionNotes').attr('placeholder', 'Optional notes...');
            }
        } else {
            $('#notesSection').addClass('hidden');
        }
        
        $('#actionNotes').val('');
        $('#actionModal').removeClass('hidden').addClass('flex');
    }

    function closeActionModal() {
        $('#actionModal').addClass('hidden').removeClass('flex');
        currentAction = null;
        currentWithdrawalId = null;
    }

    function executeAction() {
        const notes = $('#actionNotes').val().trim();
        
        if (currentAction === 'reject' && !notes) {
            showToast('Please provide a reason for rejection', 'error');
            return;
        }
        
        const endpoint = currentAction === 'approve' ? 'approve-withdrawal' : 'reject-withdrawal';
        
        showLoading();
        
        $.ajax({
            url: `${ADMIN_BASE_URL}/${endpoint}`,
            method: 'POST',
            data: {
                withdrawal_id: currentWithdrawalId,
                notes: notes,
                [CSRF_TOKEN]: CSRF_HASH
            },
            success: function(response) {
                hideLoading();
                closeActionModal();
                
                if (response.success) {
                    showToast(response.message, 'success');
                    loadPayoutRequests();
                } else {
                    showToast(response.message || 'Action failed', 'error');
                }
            },
            error: function() {
                hideLoading();
                closeActionModal();
                showToast('Error processing request', 'error');
            }
        });
    }

    function showWithdrawalModal(data) {
        const withdrawal = data.withdrawal;
        const performance = data.affiliate_performance || {
            total_referrals: 0,
            total_earnings: 0,
            total_revenue: 0,
            tier: 'Bronze',
            commission_rate: 0,
            last_30_days: {
                referrals: 0,
                earnings: 0,
                revenue: 0
            }
        };
        
        const modalContent = `
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Withdrawal Details -->
                <div class="space-y-4">
                    <h4 class="text-lg font-semibold text-gray-900">Withdrawal Details</h4>
                    
                    <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Transaction ID:</span>
                            <span class="font-medium">${withdrawal.transaction_id}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Amount:</span>
                            <span class="font-medium text-lg">₹${formatAmount(withdrawal.amount)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Status:</span>
                            <span>${getStatusBadge(withdrawal.status)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Request Date:</span>
                            <span class="font-medium">${formatDate(withdrawal.created_at)}</span>
                        </div>
                        ${withdrawal.processed_at ? `
                            <div class="flex justify-between">
                                <span class="text-gray-600">Processed Date:</span>
                                <span class="font-medium">${formatDate(withdrawal.processed_at)}</span>
                            </div>
                        ` : ''}
                        ${withdrawal.admin_notes ? `
                            <div>
                                <span class="text-gray-600">Admin Notes:</span>
                                <p class="mt-1 text-sm bg-white p-2 rounded border">${withdrawal.admin_notes}</p>
                            </div>
                        ` : ''}
                    </div>
                    
                    <!-- User Details -->
                    <h4 class="text-lg font-semibold text-gray-900">User Details</h4>
                    <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Name:</span>
                            <span class="font-medium">${withdrawal.full_name}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Email:</span>
                            <span class="font-medium">${withdrawal.email}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Affiliate Code:</span>
                            <span class="font-medium">${withdrawal.refer_code}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Wallet Balance:</span>
                            <span class="font-medium">₹${formatAmount(withdrawal.wallet_balance)}</span>
                        </div>
                    </div>
                    
                    <!-- Bank Details -->
                    <h4 class="text-lg font-semibold text-gray-900">Bank Details</h4>
                    <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                        <div class="bank-detail-row flex justify-between items-center p-2 rounded">
                            <span class="text-gray-600">Account Name:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-medium select-all" id="accountName">${withdrawal.account_name}</span>
                                <button onclick="copyToClipboard('accountName', 'Account name')"
                                        class="copy-btn text-gray-400 hover:text-indigo-600 p-1 rounded"
                                        title="Copy account name">
                                    <i class="fas fa-copy text-sm"></i>
                                </button>
                            </div>
                        </div>
                        <div class="bank-detail-row flex justify-between items-center p-2 rounded">
                            <span class="text-gray-600">Bank Name:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-medium select-all" id="bankName">${withdrawal.bank_name}</span>
                                <button onclick="copyToClipboard('bankName', 'Bank name')"
                                        class="copy-btn text-gray-400 hover:text-indigo-600 p-1 rounded"
                                        title="Copy bank name">
                                    <i class="fas fa-copy text-sm"></i>
                                </button>
                            </div>
                        </div>
                        <div class="bank-detail-row flex justify-between items-center p-2 rounded">
                            <span class="text-gray-600">Account Number:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-medium select-all font-mono" id="accountNumber">${withdrawal.account_number}</span>
                                <button onclick="copyToClipboard('accountNumber', 'Account number')"
                                        class="copy-btn text-gray-400 hover:text-indigo-600 p-1 rounded"
                                        title="Copy account number">
                                    <i class="fas fa-copy text-sm"></i>
                                </button>
                            </div>
                        </div>
                        <div class="bank-detail-row flex justify-between items-center p-2 rounded">
                            <span class="text-gray-600">IFSC Code:</span>
                            <div class="flex items-center space-x-2">
                                <span class="font-medium select-all font-mono" id="ifscCode">${withdrawal.ifsc_code}</span>
                                <button onclick="copyToClipboard('ifscCode', 'IFSC code')"
                                        class="copy-btn text-gray-400 hover:text-indigo-600 p-1 rounded"
                                        title="Copy IFSC code">
                                    <i class="fas fa-copy text-sm"></i>
                                </button>
                            </div>
                        </div>
                        ${withdrawal.branch_name ? `
                            <div class="bank-detail-row flex justify-between items-center p-2 rounded">
                                <span class="text-gray-600">Branch:</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-medium select-all" id="branchName">${withdrawal.branch_name}</span>
                                    <button onclick="copyToClipboard('branchName', 'Branch name')"
                                            class="copy-btn text-gray-400 hover:text-indigo-600 p-1 rounded"
                                            title="Copy branch name">
                                        <i class="fas fa-copy text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        ` : ''}


                    </div>
                </div>
                
                <!-- Affiliate Performance -->
                <div class="space-y-4">
                    <h4 class="text-lg font-semibold text-gray-900">Affiliate Performance</h4>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-blue-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-blue-600">${performance?.total_referrals || 0}</div>
                            <div class="text-sm text-blue-800">Total Referrals</div>
                        </div>
                        <div class="bg-green-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-green-600">₹${formatAmount(performance?.total_earnings || 0)}</div>
                            <div class="text-sm text-green-800">Total Earnings</div>
                        </div>
                        <div class="bg-purple-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-purple-600">₹${formatAmount(performance?.total_revenue || 0)}</div>
                            <div class="text-sm text-purple-800">Revenue Generated</div>
                        </div>
                        <div class="bg-yellow-50 rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-yellow-600">${performance?.tier || 'Bronze'}</div>
                            <div class="text-sm text-yellow-800">${performance?.commission_rate || 0}% Commission</div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h5 class="font-semibold text-gray-900 mb-3">Last 30 Days Performance</h5>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Referrals:</span>
                                <span class="font-medium">${performance?.last_30_days?.referrals || 0}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Earnings:</span>
                                <span class="font-medium">₹${formatAmount(performance?.last_30_days?.earnings || 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Revenue:</span>
                                <span class="font-medium">₹${formatAmount(performance?.last_30_days?.revenue || 0)}</span>
                            </div>
                        </div>
                    </div>
                    
                    ${withdrawal.status === 'pending' ? `
                        <div class="space-y-3">
                            <button onclick="approveWithdrawal(${withdrawal.id}); closeWithdrawalModal();" 
                                    class="w-full btn-success py-3 rounded-lg text-white font-medium hover:opacity-90 transition-opacity">
                                <i class="fas fa-check mr-2"></i>Approve Withdrawal
                            </button>
                            <button onclick="rejectWithdrawal(${withdrawal.id}); closeWithdrawalModal();" 
                                    class="w-full btn-danger py-3 rounded-lg text-white font-medium hover:opacity-90 transition-opacity">
                                <i class="fas fa-times mr-2"></i>Reject Withdrawal
                            </button>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
        
        $('#modalContent').html(modalContent);
        $('#withdrawalModal').removeClass('hidden').addClass('flex');
    }

    function closeWithdrawalModal() {
        $('#withdrawalModal').addClass('hidden').removeClass('flex');
    }

    function exportToCSV() {
        const params = new URLSearchParams(currentFilters);
        window.open(`${ADMIN_BASE_URL}/export-payouts?${params}`, '_blank');
    }

    function formatAmount(amount) {
        // Handle null, undefined, or invalid values
        if (amount === null || amount === undefined || amount === '' || isNaN(amount)) {
            return '0.00';
        }

        const numericAmount = parseFloat(amount);

        // Handle NaN after parseFloat
        if (isNaN(numericAmount)) {
            return '0.00';
        }

        return numericAmount.toLocaleString('en-IN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Copy to clipboard functionality
    window.copyToClipboard = function(elementId, fieldName) {
        const element = document.getElementById(elementId);
        if (!element) {
            showToast('Element not found', 'error');
            return;
        }

        const text = element.textContent.trim();

        // Use the modern Clipboard API if available
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                showToast(`${fieldName} copied!`, 'success', 2000);
                // Visual feedback
                const button = event.target.closest('button');
                if (button) {
                    const icon = button.querySelector('i');
                    const originalClass = icon.className;

                    // Add copied class and change icon
                    button.classList.add('copied');
                    icon.className = 'fas fa-check text-sm';

                    // Reset after animation
                    setTimeout(() => {
                        button.classList.remove('copied');
                        icon.className = originalClass;
                    }, 1500);
                }
            }).catch(err => {
                console.error('Failed to copy: ', err);
                fallbackCopyTextToClipboard(text, fieldName);
            });
        } else {
            // Fallback for older browsers
            fallbackCopyTextToClipboard(text, fieldName);
        }
    };



    // Fallback copy function for older browsers
    function fallbackCopyTextToClipboard(text, fieldName) {
        const textArea = document.createElement("textarea");
        textArea.value = text;

        // Avoid scrolling to bottom
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        textArea.style.opacity = "0";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showToast(`${fieldName} copied!`, 'success', 2000);
                // Visual feedback
                if (event && event.target) {
                    const button = event.target.closest('button');
                    if (button) {
                        const icon = button.querySelector('i');
                        if (icon) {
                            const originalClass = icon.className;
                            button.classList.add('copied');
                            icon.className = 'fas fa-check text-sm';
                            setTimeout(() => {
                                button.classList.remove('copied');
                                icon.className = originalClass;
                            }, 1500);
                        }
                    }
                }
            } else {
                showToast('Failed to copy to clipboard', 'error');
            }
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
            showToast('Copy not supported by browser', 'error');
        }

        document.body.removeChild(textArea);
    }
});
