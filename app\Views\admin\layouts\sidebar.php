        <!-- Sidebar -->
        <div id="sidebar" class="sidebar-transition fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 text-white transform -translate-x-full lg:translate-x-0 lg:static lg:inset-0">
            <!-- Sidebar Header -->
            <div class="flex items-center justify-between h-16 px-6 bg-gray-900">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-sm"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold">Trade Diary</h1>
                        <p class="text-xs text-gray-400">Admin Panel</p>
                    </div>
                </div>
                <button id="closeSidebar" class="lg:hidden text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Navigation -->
            <nav class="mt-6 px-3">
                <div class="space-y-1">
                    <!-- Dashboard -->
                    <a href="<?= base_url('admin/dashboard') ?>" 
                       class="nav-link flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= ($active ?? '') === 'dashboard' ? 'bg-indigo-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>

                    <!-- Payout Management -->
                    <div class="space-y-1">
                        <div class="flex items-center px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                            <i class="fas fa-money-bill-wave mr-2"></i>
                            Payout Management
                        </div>
                        
                        <a href="<?= base_url('admin/payouts') ?>" 
                           class="nav-link flex items-center px-3 py-2 ml-4 text-sm font-medium rounded-lg transition-colors duration-200 <?= ($active ?? '') === 'payouts' ? 'bg-indigo-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>">
                            <i class="fas fa-credit-card mr-3"></i>
                            Payout Requests
                            <?php if (isset($pending_count) && $pending_count > 0): ?>
                                <span class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1"><?= $pending_count ?></span>
                            <?php endif; ?>
                        </a>

                        <a href="<?= base_url('admin/withdrawals') ?>" 
                           class="nav-link flex items-center px-3 py-2 ml-4 text-sm font-medium rounded-lg transition-colors duration-200 <?= ($active ?? '') === 'withdrawals' ? 'bg-indigo-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>">
                            <i class="fas fa-history mr-3"></i>
                            Withdrawal History
                        </a>
                    </div>

                    <!-- User Management -->
                    <div class="space-y-1">
                        <div class="flex items-center px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                            <i class="fas fa-users mr-2"></i>
                            User Management
                        </div>
                        
                        <a href="<?= base_url('admin/users') ?>" 
                           class="nav-link flex items-center px-3 py-2 ml-4 text-sm font-medium rounded-lg transition-colors duration-200 <?= ($active ?? '') === 'users' ? 'bg-indigo-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>">
                            <i class="fas fa-user-friends mr-3"></i>
                            All Users
                        </a>

                        <a href="<?= base_url('admin/affiliates') ?>" 
                           class="nav-link flex items-center px-3 py-2 ml-4 text-sm font-medium rounded-lg transition-colors duration-200 <?= ($active ?? '') === 'affiliates' ? 'bg-indigo-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>">
                            <i class="fas fa-handshake mr-3"></i>
                            Affiliates
                        </a>
                    </div>

                    <!-- Reports -->
                    <div class="space-y-1">
                        <div class="flex items-center px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                            <i class="fas fa-chart-bar mr-2"></i>
                            Reports & Analytics
                        </div>
                        
                        <a href="<?= base_url('admin/reports/referrals') ?>" 
                           class="nav-link flex items-center px-3 py-2 ml-4 text-sm font-medium rounded-lg transition-colors duration-200 <?= ($active ?? '') === 'referral-reports' ? 'bg-indigo-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>">
                            <i class="fas fa-chart-pie mr-3"></i>
                            Referral Reports
                        </a>

                        <a href="<?= base_url('admin/reports/financial') ?>" 
                           class="nav-link flex items-center px-3 py-2 ml-4 text-sm font-medium rounded-lg transition-colors duration-200 <?= ($active ?? '') === 'financial-reports' ? 'bg-indigo-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>">
                            <i class="fas fa-dollar-sign mr-3"></i>
                            Financial Reports
                        </a>
                    </div>

                    <!-- Settings -->
                    <div class="space-y-1">
                        <div class="flex items-center px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                            <i class="fas fa-cog mr-2"></i>
                            Settings
                        </div>
                        
                        <a href="<?= base_url('admin/settings') ?>" 
                           class="nav-link flex items-center px-3 py-2 ml-4 text-sm font-medium rounded-lg transition-colors duration-200 <?= ($active ?? '') === 'settings' ? 'bg-indigo-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>">
                            <i class="fas fa-sliders-h mr-3"></i>
                            System Settings
                        </a>

                        <a href="<?= base_url('admin/admins') ?>" 
                           class="nav-link flex items-center px-3 py-2 ml-4 text-sm font-medium rounded-lg transition-colors duration-200 <?= ($active ?? '') === 'admins' ? 'bg-indigo-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>">
                            <i class="fas fa-user-shield mr-3"></i>
                            Admin Users
                        </a>
                    </div>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="absolute bottom-0 left-0 right-0 p-4 bg-gray-900">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-white truncate">
                            <?= session('admin_full_name') ?? 'Admin User' ?>
                        </p>
                        <p class="text-xs text-gray-400 truncate">
                            <?= ucfirst(session('admin_role') ?? 'admin') ?>
                        </p>
                    </div>
                    <div class="relative">
                        <button id="userMenuButton" class="text-gray-400 hover:text-white">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div id="userMenu" class="hidden absolute bottom-full right-0 mb-2 w-48 bg-white rounded-lg shadow-lg py-1 z-50">
                            <a href="<?= base_url('admin/profile') ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user mr-2"></i>Profile
                            </a>
                            <a href="<?= base_url('admin/change-password') ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-key mr-2"></i>Change Password
                            </a>
                            <hr class="my-1">
                            <a href="<?= base_url('admin/logout') ?>" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
