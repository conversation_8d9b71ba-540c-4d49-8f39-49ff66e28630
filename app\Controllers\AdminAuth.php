<?php

namespace App\Controllers;

use App\Models\AdminModel;

class AdminAuth extends BaseController
{
    protected $adminModel;
    protected $session;

    public function __construct()
    {
        $this->adminModel = new AdminModel();
        $this->session = \Config\Services::session();
        
        // Create default admin if none exists
        $this->adminModel->createDefaultAdmin();

        // Update existing admin password to new credentials
        $this->adminModel->updateDefaultAdminPassword();
    }

    /**
     * Show admin login page
     */
    public function login()
    {
        // Redirect if already logged in
        if ($this->session->get('admin_logged_in')) {
            return redirect()->to('/admin/dashboard');
        }

        $data = [
            'title' => 'Admin Login - Trade Diary',
            'error' => $this->session->getFlashdata('error'),
            'success' => $this->session->getFlashdata('success')
        ];

        return view('admin/auth/login', $data);
    }

    /**
     * Process admin login
     */
    public function processLogin()
    {
        $rules = [
            'username' => 'required',
            'password' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('error', 'Please fill in all required fields.');
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');
        $remember = $this->request->getPost('remember');

        // Debug logging
        log_message('debug', 'Login attempt - Username: ' . $username);

        // Try direct database query as fallback
        $db = \Config\Database::connect();
        $query = $db->query("SELECT * FROM admins WHERE username = ? OR email = ?", [$username, $username]);
        $admin = $query->getRowArray();

        if ($admin && $admin['status'] === 'active' && password_verify($password, $admin['password'])) {
            // Direct login success
            $result = $admin;
            unset($result['password']);
            log_message('debug', 'Direct database login successful');
        } else {
            // Fallback to model method
            $result = $this->adminModel->verifyCredentials($username, $password);
        }

        // Debug logging
        log_message('debug', 'Login result: ' . json_encode($result));

        if ($result === false) {
            log_message('debug', 'Login failed - Invalid credentials');
            return redirect()->back()->withInput()->with('error', 'Invalid username or password.');
        }

        if (isset($result['error'])) {
            log_message('debug', 'Login failed - Error: ' . $result['error']);
            return redirect()->back()->withInput()->with('error', $result['error']);
        }

        // Set session data
        $sessionData = [
            'admin_id' => $result['id'],
            'admin_username' => $result['username'],
            'admin_email' => $result['email'],
            'admin_full_name' => $result['full_name'],
            'admin_role' => $result['role'],
            'admin_permissions' => $this->adminModel->getPermissions($result['role']),
            'admin_logged_in' => true
        ];

        $this->session->set($sessionData);

        // Set remember me cookie if requested
        if ($remember) {
            $cookieData = [
                'name' => 'admin_remember',
                'value' => base64_encode($result['id'] . '|' . $result['username']),
                'expire' => 86400 * 30, // 30 days
                'httponly' => true,
                'secure' => is_https()
            ];
            $this->response->setCookie($cookieData);
        }

        return redirect()->to('/admin/dashboard')->with('success', 'Welcome back, ' . $result['full_name'] . '!');
    }

    /**
     * Debug method to check and recreate admin account
     */
    public function debugAdmin()
    {
        // Check if admin exists
        $admin = $this->adminModel->where('username', 'admin')->first();

        $output = "<h2>Admin Debug Information</h2>";

        if ($admin) {
            $output .= "<p><strong>Admin account exists:</strong></p>";
            $output .= "<pre>" . print_r($admin, true) . "</pre>";

            // Test password verification
            $passwordTest = password_verify('TradeDiary@2025', $admin['password']);
            $output .= "<p><strong>Password verification test:</strong> " . ($passwordTest ? 'PASS' : 'FAIL') . "</p>";
        } else {
            $output .= "<p><strong>Admin account does not exist!</strong></p>";

            // Try to create it
            $created = $this->adminModel->forceCreateDefaultAdmin();
            $output .= "<p><strong>Attempted to create admin:</strong> " . ($created ? 'SUCCESS' : 'FAILED') . "</p>";

            if ($created) {
                $admin = $this->adminModel->where('username', 'admin')->first();
                $output .= "<p><strong>Newly created admin:</strong></p>";
                $output .= "<pre>" . print_r($admin, true) . "</pre>";
            }
        }

        // Show database errors if any
        $errors = $this->adminModel->errors();
        if (!empty($errors)) {
            $output .= "<p><strong>Model Errors:</strong></p>";
            $output .= "<pre>" . print_r($errors, true) . "</pre>";
        }

        return $output;
    }

    /**
     * Admin logout
     */
    public function logout()
    {
        // Clear session data
        $this->session->remove([
            'admin_id',
            'admin_username', 
            'admin_email',
            'admin_full_name',
            'admin_role',
            'admin_permissions',
            'admin_logged_in'
        ]);

        // Clear remember me cookie
        $this->response->setCookie([
            'name' => 'admin_remember',
            'value' => '',
            'expire' => -1
        ]);

        return redirect()->to('/admin/login')->with('success', 'You have been logged out successfully.');
    }

    /**
     * Check if admin is logged in (for AJAX requests)
     */
    public function checkAuth()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403)->setJSON(['error' => 'Forbidden']);
        }

        $isLoggedIn = $this->session->get('admin_logged_in');
        
        return $this->response->setJSON([
            'authenticated' => (bool)$isLoggedIn,
            'admin' => $isLoggedIn ? [
                'id' => $this->session->get('admin_id'),
                'username' => $this->session->get('admin_username'),
                'full_name' => $this->session->get('admin_full_name'),
                'role' => $this->session->get('admin_role')
            ] : null
        ]);
    }

    /**
     * Change admin password
     */
    public function changePassword()
    {
        if (!$this->session->get('admin_logged_in')) {
            return redirect()->to('/admin/login');
        }

        if ($this->request->getMethod() === 'POST') {
            $rules = [
                'current_password' => 'required',
                'new_password' => 'required|min_length[8]',
                'confirm_password' => 'required|matches[new_password]'
            ];

            if (!$this->validate($rules)) {
                return redirect()->back()->with('error', 'Please check your input and try again.');
            }

            $adminId = $this->session->get('admin_id');
            $currentPassword = $this->request->getPost('current_password');
            $newPassword = $this->request->getPost('new_password');

            $admin = $this->adminModel->find($adminId);
            
            if (!password_verify($currentPassword, $admin['password'])) {
                return redirect()->back()->with('error', 'Current password is incorrect.');
            }

            $this->adminModel->update($adminId, ['password' => $newPassword]);

            return redirect()->back()->with('success', 'Password changed successfully.');
        }

        $data = [
            'title' => 'Change Password - Admin Panel',
            'admin' => [
                'full_name' => $this->session->get('admin_full_name'),
                'username' => $this->session->get('admin_username'),
                'role' => $this->session->get('admin_role')
            ]
        ];

        return view('admin/auth/change_password', $data);
    }

    /**
     * Admin profile
     */
    public function profile()
    {
        if (!$this->session->get('admin_logged_in')) {
            return redirect()->to('/admin/login');
        }

        $adminId = $this->session->get('admin_id');
        $admin = $this->adminModel->find($adminId);

        if ($this->request->getMethod() === 'POST') {
            $rules = [
                'full_name' => 'required|min_length[2]|max_length[100]',
                'email' => 'required|valid_email'
            ];

            if (!$this->validate($rules)) {
                return redirect()->back()->withInput()->with('error', 'Please check your input and try again.');
            }

            $updateData = [
                'full_name' => $this->request->getPost('full_name'),
                'email' => $this->request->getPost('email')
            ];

            $this->adminModel->update($adminId, $updateData);

            // Update session data
            $this->session->set([
                'admin_full_name' => $updateData['full_name'],
                'admin_email' => $updateData['email']
            ]);

            return redirect()->back()->with('success', 'Profile updated successfully.');
        }

        $data = [
            'title' => 'Admin Profile - Admin Panel',
            'admin' => $admin
        ];

        return view('admin/auth/profile', $data);
    }
}
