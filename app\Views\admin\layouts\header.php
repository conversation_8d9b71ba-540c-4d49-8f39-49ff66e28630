<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Admin Panel - Trade Diary' ?></title>
    
    <!-- CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="<?= base_url('assets/admin/css/admin.css') ?>">
    
    <!-- Custom Styles -->
    <style>
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        
        .sidebar-overlay {
            background: rgba(0, 0, 0, 0.5);
            transition: opacity 0.3s ease-in-out;
        }
        
        .sidebar-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .sidebar-overlay:not(.hidden) {
            opacity: 1;
        }
        
        .table-hover tbody tr:hover {
            background-color: #f8fafc;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: capitalize;
        }
        
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .status-approved {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .status-rejected {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .admin-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .stats-card {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        
        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            padding: 1rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        
        .toast.show {
            transform: translateX(0);
        }
        
        .toast-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .toast-error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        
        .toast-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        
        .toast-info {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }
        
        /* Dark mode styles */
        .dark .bg-gray-50 {
            background-color: #1f2937;
        }
        
        .dark .bg-white {
            background-color: #374151;
        }
        
        .dark .text-gray-900 {
            color: #f9fafb;
        }
        
        .dark .text-gray-700 {
            color: #d1d5db;
        }
        
        .dark .border-gray-200 {
            border-color: #4b5563;
        }

        /* Tooltip styles */
        .tooltip {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: 9999;
            pointer-events: none;
        }

        /* Copy button hover effects */
        .copy-btn {
            transition: all 0.2s ease-in-out;
        }

        .copy-btn:hover {
            transform: scale(1.1);
        }

        .copy-btn.copied {
            color: #10b981 !important;
            transform: scale(1.2);
        }

        /* Bank details section styling */
        .bank-detail-row {
            transition: background-color 0.2s ease-in-out;
        }

        .bank-detail-row:hover {
            background-color: rgba(99, 102, 241, 0.05);
        }

        /* Sidebar Collapse Styles */
        :root {
            --sidebar-width: 16rem;
            --sidebar-collapsed-width: 4rem;
        }

        .sidebar-collapsed {
            --sidebar-width: var(--sidebar-collapsed-width);
        }

        .sidebar-collapsed .sidebar-text {
            opacity: 0;
            visibility: hidden;
            width: 0;
            overflow: hidden;
        }

        .sidebar-collapsed .sidebar-submenu {
            display: none;
        }

        .sidebar-collapsed .sidebar-section-toggle {
            justify-content: center;
        }

        .sidebar-collapsed .nav-link {
            justify-content: center;
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        .sidebar-collapsed #sidebarBrand {
            justify-content: center;
        }

        /* Sidebar Submenu Animation */
        .sidebar-submenu {
            max-height: 500px;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
        }

        .sidebar-submenu.collapsed {
            max-height: 0;
            opacity: 0;
        }

        /* Dark Mode Styles */
        .dark {
            color-scheme: dark;
        }

        .dark body {
            background-color: #111827;
            color: #f9fafb;
        }

        .dark .bg-gray-50 {
            background-color: #1f2937;
        }

        .dark .bg-white {
            background-color: #374151;
        }

        .dark .bg-gray-100 {
            background-color: #4b5563;
        }

        .dark .bg-gray-200 {
            background-color: #6b7280;
        }

        .dark .text-gray-900 {
            color: #f9fafb;
        }

        .dark .text-gray-700 {
            color: #d1d5db;
        }

        .dark .text-gray-600 {
            color: #9ca3af;
        }

        .dark .text-gray-500 {
            color: #6b7280;
        }

        .dark .border-gray-200 {
            border-color: #4b5563;
        }

        .dark .border-gray-300 {
            border-color: #6b7280;
        }

        /* Dark Mode Switch */
        .dark-mode-active #darkModeSwitch {
            background-color: #4f46e5;
        }

        .dark-mode-active #darkModeSwitchHandle {
            transform: translateX(1.25rem);
        }

        .dark-mode-active #darkModeIcon {
            transform: rotate(180deg);
        }

        /* Tooltip for collapsed sidebar */
        .sidebar-collapsed [data-tooltip]:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            margin-left: 0.5rem;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: 9999;
            pointer-events: none;
        }

        /* Smooth transitions */
        .sidebar-transition {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Rotation utilities */
        .rotate-180 {
            transform: rotate(180deg);
        }

        /* Hover effects for sidebar items */
        .nav-link:hover {
            transform: translateX(2px);
        }

        .sidebar-collapsed .nav-link:hover {
            transform: none;
        }

        /* Main content adjustment for collapsed sidebar */
        .main-content {
            margin-left: var(--sidebar-width);
            transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @media (max-width: 1024px) {
            .main-content {
                margin-left: 0;
            }
        }
    </style>
    
    <!-- Global Variables -->
    <script>
        const BASE_URL = '<?= base_url() ?>';
        const ADMIN_BASE_URL = '<?= base_url('admin') ?>';
        const CSRF_TOKEN = '<?= csrf_token() ?>';
        const CSRF_HASH = '<?= csrf_hash() ?>';
    </script>
</head>
<body class="bg-gray-50 font-sans antialiased">
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="loading-spinner"></div>
            <span class="text-gray-700">Loading...</span>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Main Layout Container -->
    <div class="flex h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
        <!-- Sidebar Overlay (Mobile) -->
        <div id="sidebarOverlay" class="sidebar-overlay fixed inset-0 z-40 lg:hidden hidden"></div>
