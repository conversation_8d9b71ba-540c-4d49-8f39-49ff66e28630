<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Favicon -->
    <link rel="icon" href="<?= base_url('assets/images/logo.png') ?>" type="image/png">
    <link rel="shortcut icon" href="<?= base_url('assets/images/logo.png') ?>" type="image/png">

    <!-- Title -->
    <title>Trade Diary | Smart AI-Powered Trading Journal for Traders</title>

    <!-- Meta Description -->
    <meta name="description"
        content="Trade Diary is your AI-powered trading journal. Log trades, track performance, and gain insights to become a more disciplined, profitable trader." />

    <!-- Meta Keywords (less important for SEO but still used occasionally) -->
    <meta name="keywords"
        content="Trade Diary, trading journal, trade tracker, PNL tracker, stock trading log, options trading journal, AI trading journal, trading performance" />

    <!-- Canonical URL -->
    <link rel="canonical" href="<?= base_url() ?>" />

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content="Trade Diary – Smart AI-Powered Trading Journal" />
    <meta property="og:description"
        content="Log and analyze your trades with Trade Diary – an AI-powered trading journal designed for serious traders. No fluff, just progress." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="<?= base_url() ?>" />
    <meta property="og:image" content="<?= base_url('assets/images/og_image.png') ?>" />
    <meta property="og:site_name" content="Trade Diary" />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Trade Diary – Smart AI-Powered Trading Journal">
    <meta name="twitter:description"
        content="Track and analyze your trades with AI insights, performance metrics, and discipline tools.">
    <meta name="twitter:image" content="<?= base_url('assets/images/og_image.png') ?>">


    <script src="<?= base_url() ?>assets/tailwind-3.4.16/tailwind.js"></script>

    <link rel="stylesheet" href="<?= base_url() ?>assets/font-awesome-pro-5/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
        rel="stylesheet">
    <script src="<?= base_url() ?>assets/js/tailwind-config.js"></script>
    <link rel="stylesheet" href="<?= base_url() ?>assets/css/landingStyle.css?r=<?= rand() ?>">

    <script>
        const darkMode = localStorage.getItem('darkMode');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (darkMode === 'true' || (!darkMode && prefersDark)) {
            document.documentElement.classList.add('dark');
        }
    </script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-Q8W8162DZ2"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-Q8W8162DZ2');
    </script>

    <!-- Meta Pixel Code -->
    <script>
        !function (f, b, e, v, n, t, s) {
            if (f.fbq) return; n = f.fbq = function () {
                n.callMethod ?
                n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };
            if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
            n.queue = []; t = b.createElement(e); t.async = !0;
            t.src = v; s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s)
        }(window, document, 'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '656200447449767');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id=656200447449767&ev=PageView&noscript=1" /></noscript>
    <!-- End Meta Pixel Code -->
</head>

<body class="bg-gray-50 text-gray-800 dark:bg-dark-900 dark:text-gray-200">
    <!-- Header/Navigation -->
    <header class="sticky top-0 z-50 bg-white/80 dark:bg-dark-800/80 backdrop-blur-sm shadow-sm">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16 items-center">
                <a href="<?= base_url() ?>">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 flex items-center">
                            <img src="/assets/images/logo.png" alt="Logo" class="w-8 h-8 object-contain mr-2" />
                            <span class="text-xl font-bold hidden sm:inline">Trade Diary</span>
                        </div>
                    </div>
                </a>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-center space-x-4">
                        <a href="<?= base_url() ?>#hero"
                            class="px-3 py-2 rounded-md text-sm font-medium hover:text-primary-light dark:hover:text-primary-dark">Home</a>
                        <a href="<?= base_url() ?>#features"
                            class="px-3 py-2 rounded-md text-sm font-medium hover:text-primary-light dark:hover:text-primary-dark">Features</a>
                        <a href="<?= base_url() ?>#how-it-works"
                            class="px-3 py-2 rounded-md text-sm font-medium hover:text-primary-light dark:hover:text-primary-dark">How
                            It Works</a>
                        <a href="<?= base_url() ?>#testimonials"
                            class="px-3 py-2 rounded-md text-sm font-medium hover:text-primary-light dark:hover:text-primary-dark">Testimonials</a>
                        <a href="<?= base_url() ?>#pricing"
                            class="px-3 py-2 rounded-md text-sm font-medium hover:text-primary-light dark:hover:text-primary-dark">Pricing</a>
                        <a href="<?= base_url() ?>#faq"
                            class="px-3 py-2 rounded-md text-sm font-medium hover:text-primary-light dark:hover:text-primary-dark">FAQ</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative inline-block w-10 mr-2 align-middle select-none">
                        <input type="checkbox" name="toggle" id="toggle"
                            class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
                        <label for="toggle"
                            class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                    </div>
                    <a href="<?= base_url('Login') ?>"
                        class="bg-primary-light dark:bg-primary-dark text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-opacity-90 transition">Login</a>
                    <div class="-mr-2 flex md:hidden">
                        <button type="button" id="mobile-menu-button"
                            class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-light">
                            <span class="sr-only">Open main menu</span>
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <div class="hidden md:hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="<?= base_url() ?>#home"
                    class="block px-3 py-2 rounded-md text-base font-medium hover:text-primary-light dark:hover:text-primary-dark">Home</a>
                <a href="<?= base_url() ?>#features"
                    class="block px-3 py-2 rounded-md text-base font-medium hover:text-primary-light dark:hover:text-primary-dark">Features</a>
                <a href="<?= base_url() ?>#how-it-works"
                    class="block px-3 py-2 rounded-md text-base font-medium hover:text-primary-light dark:hover:text-primary-dark">How
                    It Works</a>
                <a href="<?= base_url() ?>#testimonials"
                    class="block px-3 py-2 rounded-md text-base font-medium hover:text-primary-light dark:hover:text-primary-dark">Testimonials</a>
                <a href="<?= base_url() ?>#pricing"
                    class="block px-3 py-2 rounded-md text-base font-medium hover:text-primary-light dark:hover:text-primary-dark">Pricing</a>
                <a href="<?= base_url() ?>#faq"
                    class="block px-3 py-2 rounded-md text-base font-medium hover:text-primary-light dark:hover:text-primary-dark">FAQ</a>
            </div>
        </div>
    </header>