<?php

namespace App\Controllers;

use App\Models\ContactModel;
use CodeIgniter\HTTP\ResponseInterface;

class ContactController extends BaseController
{
    protected $contactModel;
    protected $validation;

    public function __construct()
    {
        $this->contactModel = new ContactModel();
        $this->validation = \Config\Services::validation();
    }

    /**
     * Display contact form
     */
    public function index()
    {
        $data['main_content'] = 'landing/contact';
        return view('landing/includes/template', $data);
    }

    /**
     * Handle contact form submission
     */
    public function submit()
    {
        // Set JSON response header
        $this->response->setContentType('application/json');

        try {
            // Get POST data
            $input = $this->request->getJSON(true);
            
            if (empty($input)) {
                $input = $this->request->getPost();
            }

            if (empty($input)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'No data received'
                ]);
            }

            // Validate input
            $rules = [
                'name'    => 'required|min_length[2]|max_length[255]',
                'email'   => 'required|valid_email|max_length[255]',
                'subject' => 'required|in_list[support,feedback,partnership,other]',
                'message' => 'required|min_length[10]|max_length[5000]'
            ];

            if (!$this->validation->setRules($rules)->run($input)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors'  => $this->validation->getErrors()
                ]);
            }

            // Sanitize input
            $data = [
                'name'    => trim(strip_tags($input['name'])),
                'email'   => trim(strtolower($input['email'])),
                'subject' => $input['subject'],
                'message' => trim(strip_tags($input['message'])),
                'status'  => 'pending'
            ];

            // Check for spam (basic protection)
            if ($this->isSpam($data)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Message flagged as spam'
                ]);
            }

            // Save to database
            $messageId = $this->contactModel->insert($data);

            if (!$messageId) {
                log_message('error', 'Failed to save contact message: ' . json_encode($this->contactModel->errors()));
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to save message. Please try again.'
                ]);
            }

            // Send email notification (optional)
            $this->sendEmailNotification($data, $messageId);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Your message has been sent successfully! We will get back to you soon.',
                'id'      => $messageId
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Contact form error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred. Please try again later.'
            ]);
        }
    }

    /**
     * Basic spam detection
     */
    private function isSpam($data)
    {
        // Check for common spam patterns
        $spamWords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations', 'click here', 'free money'];
        $message = strtolower($data['message'] . ' ' . $data['name']);
        
        foreach ($spamWords as $word) {
            if (strpos($message, $word) !== false) {
                return true;
            }
        }

        // Check for excessive links
        if (substr_count($data['message'], 'http') > 2) {
            return true;
        }

        // Check for repeated characters
        if (preg_match('/(.)\1{10,}/', $data['message'])) {
            return true;
        }

        return false;
    }

    /**
     * Send email notification
     */
    private function sendEmailNotification($data, $messageId)
    {
        try {
            $email = \Config\Services::email();
            
            $config = [
                'protocol'    => 'mail',
                'SMTPHost'    => 'localhost',
                'SMTPUser'    => '',
                'SMTPPass'    => '',
                'SMTPPort'    => 25,
                'SMTPCrypto'  => '',
                'mailType'    => 'html',
                'charset'     => 'utf-8',
                'validate'    => true,
                'priority'    => 3,
                'CRLF'        => "\r\n",
                'newline'     => "\r\n"
            ];

            $email->initialize($config);

            $subject_map = [
                'support'     => 'Support Request',
                'feedback'    => 'Feedback',
                'partnership' => 'Partnership Inquiry',
                'other'       => 'General Inquiry'
            ];

            $email->setFrom('<EMAIL>', 'Trade Diary Contact Form');
            $email->setTo('<EMAIL>');
            $email->setReplyTo($data['email'], $data['name']);
            
            $email->setSubject('[Trade Diary] ' . ($subject_map[$data['subject']] ?? 'Contact Form') . ' from ' . $data['name']);
            
            $emailBody = $this->generateEmailTemplate($data, $messageId);
            $email->setMessage($emailBody);

            $email->send();
            
        } catch (\Exception $e) {
            log_message('error', 'Failed to send email notification: ' . $e->getMessage());
        }
    }

    /**
     * Generate email template
     */
    private function generateEmailTemplate($data, $messageId)
    {
        $timestamp = date('Y-m-d H:i:s');
        
        return "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #4f46e5; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .field { margin-bottom: 15px; }
                .label { font-weight: bold; color: #555; }
                .value { margin-top: 5px; padding: 10px; background: white; border-radius: 5px; }
                .footer { padding: 15px; text-align: center; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>New Contact Form Submission</h2>
                </div>
                <div class='content'>
                    <div class='field'>
                        <div class='label'>Message ID:</div>
                        <div class='value'>#{$messageId}</div>
                    </div>
                    <div class='field'>
                        <div class='label'>Name:</div>
                        <div class='value'>{$data['name']}</div>
                    </div>
                    <div class='field'>
                        <div class='label'>Email:</div>
                        <div class='value'>{$data['email']}</div>
                    </div>
                    <div class='field'>
                        <div class='label'>Subject:</div>
                        <div class='value'>{$data['subject']}</div>
                    </div>
                    <div class='field'>
                        <div class='label'>Message:</div>
                        <div class='value'>" . nl2br(htmlspecialchars($data['message'])) . "</div>
                    </div>
                </div>
                <div class='footer'>
                    <p>This message was sent from the Trade Diary contact form on {$timestamp}</p>
                </div>
            </div>
        </body>
        </html>";
    }
}
