/* Admin Panel Custom Styles */

/* Sidebar Animations */
.sidebar-text {
    transition: opacity 0.3s ease, visibility 0.3s ease, width 0.3s ease;
}

.sidebar-submenu {
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
                opacity 0.3s ease;
}

/* Dark Mode Switch Animation */
#darkModeSwitch {
    transition: background-color 0.3s ease;
}

#darkModeSwitchHandle {
    transition: transform 0.3s ease;
}

#darkModeIcon {
    transition: transform 0.3s ease;
}

/* Hover Effects */
.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.nav-link:hover::before {
    left: 100%;
}

/* Sidebar Section Toggle Hover */
.sidebar-section-toggle:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* <PERSON><PERSON> Animations */
.copy-btn {
    position: relative;
    overflow: hidden;
}

.copy-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.copy-btn:active::after {
    width: 100px;
    height: 100px;
}

/* Status Badge Animations */
.status-badge {
    transition: all 0.2s ease;
}

.status-badge:hover {
    transform: scale(1.05);
}

/* Card Hover Effects */
.stats-card {
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Loading Animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Notification Badge Animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.notification-badge {
    animation: bounce 1s ease infinite;
}

/* Smooth Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Dark mode scrollbar */
.dark .custom-scrollbar::-webkit-scrollbar-track {
    background: #374151;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #6b7280;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .sidebar-text {
        font-size: 0.875rem;
    }
    
    .stats-card {
        padding: 1rem;
    }
    
    .nav-link {
        padding: 0.75rem;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .sidebar-overlay,
    header,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* Focus Styles for Accessibility */
.nav-link:focus,
.sidebar-section-toggle:focus,
button:focus {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .nav-link {
        border: 1px solid currentColor;
    }
    
    .stats-card {
        border: 2px solid currentColor;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Custom Utilities */
.text-shadow {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
}

.gradient-border {
    background: linear-gradient(45deg, #4f46e5, #7c3aed);
    padding: 1px;
    border-radius: 0.5rem;
}

.gradient-border > * {
    background: white;
    border-radius: calc(0.5rem - 1px);
}

.dark .gradient-border > * {
    background: #374151;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Success/Error State Animations */
.success-pulse {
    animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
    0% {
        transform: scale(1);
        background-color: currentColor;
    }
    50% {
        transform: scale(1.1);
        background-color: #10b981;
    }
    100% {
        transform: scale(1);
        background-color: currentColor;
    }
}
