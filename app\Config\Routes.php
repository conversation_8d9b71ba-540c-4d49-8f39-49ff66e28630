<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
// $routes->add('/Landing', 'LandingController::index');
$routes->add('/Login', 'Home::Login');
$routes->add('/contact-us', 'LandingController::ContactUs');
$routes->post('/contact/submit', 'ContactController::submit');
$routes->add('/privacy-policy', 'LandingController::PrivacyPolicy');
$routes->add('/about-us', 'LandingController::AboutUs');
$routes->add('/disclaimer', 'LandingController::Disclaimer');
$routes->add('/refund-and-cancellation', 'LandingController::Refund');
$routes->add('/disclosures', 'LandingController::Disclosures');
$routes->add('/terms-and-conditions', 'LandingController::Terms');
$routes->add('/shipping-policy', 'LandingController::Shipping');


// $routes->add('/', 'Home::Login');
$routes->add('/', 'LandingController::index');
$routes->add('/r/(:any)', 'LandingController::index');
// $routes->add('/testlogin', 'Home::LoginX');
$routes->add('/signup', 'Home::signup');
$routes->add('/signin', 'Home::signin');
$routes->add('/logout', 'Home::logout');
$routes->get('/verify-email/(:any)', 'Home::verifyEmail/$1');
$routes->add('/dashboard', 'Home::index');
$routes->add('/saveTrade', 'Home::saveTrade');

$routes->add('/googleCallback', 'Auth::googleCallback');
$routes->add('/googleLogin', 'Auth::googleLogin');

$routes->get('ajax-trades', 'Home::ajaxTrades');

$routes->add('myTrades', 'Home::myTrades');
$routes->add('deleteTrade', 'Home::deleteTrade');
$routes->add('viewTrade', 'Home::viewTrade');
$routes->add('getEditTradeData', 'Home::getEditTradeData');

$routes->add('getDashboardMetrics', 'Home::getDashboardMetrics');
$routes->add('getEquityChartData', 'Home::getEquityChartData');
$routes->add('fetchTopTrades', 'Home::fetchTopTrades');
$routes->add('getWinLossChartData', 'Home::getWinLossChartData');
$routes->add('getMonthlyConfidenceScore', 'Home::getMonthlyConfidenceScore');
$routes->add('getStrategyPerformance', 'Home::getStrategyPerformance');
$routes->add('getDailyPnl', 'Home::getDailyPnl');
$routes->add('getStrategyPnls', 'Home::getStrategyPnls');
$routes->add('getMistakesPieData', 'Home::getMistakesPieData');
$routes->add('Tutorials', 'Home::Tutorials');


$routes->add('Reports', 'Reports::Reports');
$routes->add('getTradePerformance', 'Reports::getTradePerformance');
$routes->add('getDailyPerformance', 'Reports::getDailyPerformance');
$routes->add('getTimeMetrics', 'Reports::getTimeMetrics');
$routes->add('getRiskManagementMetrics', 'Reports::getRiskManagementMetrics');
$routes->add('getEmotionalStateMetrics', 'Reports::getEmotionalStateMetrics');
$routes->add('getTargetOutcomeMetrics', 'Reports::getTargetOutcomeMetrics');
$routes->add('getSetupEffectiveness', 'Reports::getSetupEffectiveness');
$routes->add('getSymbolFrequency', 'Reports::getSymbolFrequency');
$routes->add('getAvgRRByEmotion', 'Reports::getAvgRRByEmotion');
$routes->add('getTradingOutcomes', 'Reports::getTradingOutcomes');
$routes->add('getTradesPerDay', 'Reports::getTradesPerDay');
$routes->add('getQuantityAnalysis', 'Reports::getQuantityAnalysis');
$routes->add('getCapitalUsage', 'Reports::getCapitalUsage');
$routes->add('getDailyTradeActivity', 'Reports::getDailyTradeActivity');
$routes->add('getTradeExecution', 'Reports::getTradeExecution');
$routes->post('get-weekday-performance', 'Reports::getWeekdayPerformance');

$routes->add('/Calender', 'Home::Calender');
$routes->get('getTradesByMonth', 'Home::getTradesByMonth');

$routes->get('Tools', 'Home::Tools');
$routes->get('Calculator', 'Home::Calculator');
$routes->get('ReturnsCalculator', 'Home::ReturnsCalculator');

$routes->get('Goals', 'Goals::index');
$routes->add('submitPnlGoalForm', 'Goals::submitPnlGoalForm');
$routes->add('updateGoalStatuses', 'Goals::updateGoalStatuses');
$routes->add('getPnlGoalDashboardStats', 'Goals::getPnlGoalDashboardStats');
$routes->add('getActiveGoals', 'Goals::getActiveGoals');

$routes->get('Strategy', 'Strategy::index');
$routes->add('fetchStrategyPerformance', 'Strategy::fetchStrategyCards');
$routes->add('recentTrades', 'Strategy::recentTrades');
$routes->add('getStrategyDetails', 'Strategy::getStrategyDetails');
$routes->add('saveUserStrategy', 'Strategy::saveUserStrategy');
$routes->add('getEditStrategyData', 'Strategy::getEditStrategyData');
$routes->add('updateStrategy', 'Strategy::updateStrategy');

$routes->add('MyProfile', 'Auth::MyProfile');
$routes->add('updateProfile', 'Auth::updateProfile');
$routes->add('saveBrokerDetails', 'Auth::saveBrokerDetails');
$routes->add('fetchConnectedBroker', 'Auth::fetchConnectedBroker');
$routes->add('deleteBroker', 'Auth::deleteBroker');
$routes->add('getDhanTradesFromPython', 'Auth::getDhanTradesFromPython');
$routes->add('syncTrades', 'Auth::syncTrades');


$routes->add('fetchCurrentUserTrades', 'TradeFetcher::fetchCurrentUserTrades');


// payments
$routes->add('/createOrder', 'PaymentController::createOrder');
$routes->add('/saveTransaction', 'PaymentController::saveTransaction');

// Affiliates
$routes->add('/AffiliateDashboard', 'Affiliate::index');
$routes->add('/SalesDashboard', 'Affiliate::Sales');
$routes->add('/fetchReferralList', 'Affiliate::referrals');
$routes->add('/PayoutDetails', 'Affiliate::PayoutDetails');
$routes->add('/savePayoutDetails', 'Affiliate::saveBank');
$routes->add('/getBankDetails', 'Affiliate::getBankDetails');
$routes->add('/requestWithdraw', 'Affiliate::requestWithdraw');
$routes->add('/getWalletStats', 'Affiliate::getWalletStats');
$routes->add('/fetchPayouts', 'Affiliate::fetchPayouts');
$routes->add('/AffiliateFAQ', 'Affiliate::AffiliateFAQ');

// Admin Routes
$routes->group('admin', ['filter' => 'admin_auth'], function($routes) {
    $routes->add('dashboard', 'Admin::dashboard');
    $routes->add('payouts', 'Admin::payouts');
    $routes->add('get-payout-requests', 'Admin::getPayoutRequests');
    $routes->post('approve-withdrawal', 'Admin::approveWithdrawal');
    $routes->post('reject-withdrawal', 'Admin::rejectWithdrawal');
    $routes->add('withdrawal-details/(:num)', 'Admin::getWithdrawalDetails/$1');
    $routes->add('export-payouts', 'Admin::exportPayouts');
});

// Admin Authentication Routes (no filter)
$routes->add('admin/login', 'AdminAuth::login');
$routes->post('admin/login', 'AdminAuth::processLogin');
$routes->add('admin/logout', 'AdminAuth::logout');
$routes->add('admin/check-auth', 'AdminAuth::checkAuth');
$routes->add('admin/change-password', 'AdminAuth::changePassword');
$routes->post('admin/change-password', 'AdminAuth::changePassword');
$routes->add('admin/profile', 'AdminAuth::profile');
$routes->post('admin/profile', 'AdminAuth::profile');
$routes->add('admin/unlock-account', 'AdminAuth::unlockAccount');
$routes->add('admin/create-admin2025', 'AdminAuth::createAdmin2025');