<?php

namespace App\Models;

use CodeIgniter\Model;

class BankDetailsModel extends Model
{
    protected $table = 'bank_details';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id',
        'account_name',
        'bank_name',
        'account_number',
        'ifsc_code',
        'branch_name'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'user_id' => 'required|integer',
        'account_name' => 'required|min_length[2]|max_length[100]',
        'bank_name' => 'required|min_length[2]|max_length[100]',
        'account_number' => 'required|min_length[8]|max_length[30]',
        'ifsc_code' => 'required|min_length[11]|max_length[11]|regex_match[/^[A-Z]{4}0[A-Z0-9]{6}$/]'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'account_name' => [
            'required' => 'Account holder name is required',
            'min_length' => 'Account holder name must be at least 2 characters',
            'max_length' => 'Account holder name cannot exceed 100 characters'
        ],
        'bank_name' => [
            'required' => 'Bank name is required',
            'min_length' => 'Bank name must be at least 2 characters',
            'max_length' => 'Bank name cannot exceed 100 characters'
        ],
        'account_number' => [
            'required' => 'Account number is required',
            'min_length' => 'Account number must be at least 8 characters',
            'max_length' => 'Account number cannot exceed 30 characters'
        ],
        'ifsc_code' => [
            'required' => 'IFSC code is required',
            'min_length' => 'IFSC code must be 11 characters',
            'max_length' => 'IFSC code must be 11 characters',
            'regex_match' => 'Please enter a valid IFSC code'
        ]
    ];

    /**
     * Get bank details by user ID
     */
    public function getByUserId($userId)
    {
        return $this->where('user_id', $userId)->first();
    }

    /**
     * Get bank details with user information
     */
    public function getBankDetailsWithUser($filters = [])
    {
        $builder = $this->db->table('bank_details bd')
            ->select('bd.*, u.full_name, u.email, u.refer_code, u.wallet_balance')
            ->join('users u', 'bd.user_id = u.id', 'left');

        // Apply filters
        if (!empty($filters['search'])) {
            $builder->groupStart()
                ->like('u.full_name', $filters['search'])
                ->orLike('u.email', $filters['search'])
                ->orLike('bd.account_name', $filters['search'])
                ->orLike('bd.bank_name', $filters['search'])
                ->orLike('bd.ifsc_code', $filters['search'])
                ->groupEnd();
        }

        if (!empty($filters['bank_name'])) {
            $builder->like('bd.bank_name', $filters['bank_name']);
        }

        return $builder->orderBy('bd.created_at', 'DESC');
    }

    /**
     * Get bank statistics
     */
    public function getBankStats()
    {
        $stats = [];

        // Total bank accounts registered
        $stats['total_accounts'] = $this->countAllResults();

        // Bank distribution
        $bankDistribution = $this->db->table('bank_details')
            ->select('bank_name, COUNT(*) as count')
            ->groupBy('bank_name')
            ->orderBy('count', 'DESC')
            ->get()
            ->getResultArray();

        $stats['bank_distribution'] = $bankDistribution;

        // Recent registrations (last 30 days)
        $recentAccounts = $this->where('created_at >=', date('Y-m-d', strtotime('-30 days')))
                              ->countAllResults();
        $stats['recent_accounts'] = $recentAccounts;

        return $stats;
    }

    /**
     * Validate IFSC code format
     */
    public function validateIFSC($ifscCode)
    {
        // IFSC format: 4 letters + 0 + 6 alphanumeric characters
        $pattern = '/^[A-Z]{4}0[A-Z0-9]{6}$/';
        return preg_match($pattern, strtoupper($ifscCode));
    }

    /**
     * Mask account number for display
     */
    public function maskAccountNumber($accountNumber)
    {
        $length = strlen($accountNumber);
        if ($length <= 4) {
            return str_repeat('*', $length);
        }
        
        return str_repeat('*', $length - 4) . substr($accountNumber, -4);
    }

    /**
     * Get bank details for withdrawal processing
     */
    public function getBankDetailsForWithdrawal($userId)
    {
        $bankDetails = $this->getByUserId($userId);
        
        if (!$bankDetails) {
            return null;
        }

        // Return formatted bank details
        return [
            'id' => $bankDetails['id'],
            'user_id' => $bankDetails['user_id'],
            'account_name' => $bankDetails['account_name'],
            'bank_name' => $bankDetails['bank_name'],
            'account_number' => $bankDetails['account_number'],
            'account_number_masked' => $this->maskAccountNumber($bankDetails['account_number']),
            'ifsc_code' => $bankDetails['ifsc_code'],
            'branch_name' => $bankDetails['branch_name'],
            'created_at' => $bankDetails['created_at'],
            'updated_at' => $bankDetails['updated_at']
        ];
    }

    /**
     * Update or create bank details
     */
    public function updateOrCreate($userId, $data)
    {
        $existing = $this->getByUserId($userId);
        
        $data['user_id'] = $userId;
        
        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            return $this->insert($data);
        }
    }

    /**
     * Get users without bank details
     */
    public function getUsersWithoutBankDetails()
    {
        return $this->db->table('users u')
            ->select('u.id, u.full_name, u.email, u.refer_code, u.wallet_balance')
            ->join('bank_details bd', 'u.id = bd.user_id', 'left')
            ->where('bd.user_id IS NULL')
            ->where('u.wallet_balance >', 0)
            ->orderBy('u.wallet_balance', 'DESC')
            ->get()
            ->getResultArray();
    }

    /**
     * Verify bank details completeness
     */
    public function verifyBankDetails($userId)
    {
        $bankDetails = $this->getByUserId($userId);
        
        if (!$bankDetails) {
            return [
                'valid' => false,
                'message' => 'No bank details found. Please add your bank details first.'
            ];
        }

        $requiredFields = ['account_name', 'bank_name', 'account_number', 'ifsc_code'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (empty($bankDetails[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            return [
                'valid' => false,
                'message' => 'Incomplete bank details. Missing: ' . implode(', ', $missingFields)
            ];
        }

        // Validate IFSC code
        if (!$this->validateIFSC($bankDetails['ifsc_code'])) {
            return [
                'valid' => false,
                'message' => 'Invalid IFSC code format.'
            ];
        }

        return [
            'valid' => true,
            'message' => 'Bank details are complete and valid.',
            'details' => $bankDetails
        ];
    }
}
