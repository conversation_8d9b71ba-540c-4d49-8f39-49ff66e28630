<?php

namespace App\Controllers;

use App\Models\AdminModel;
use App\Models\WithdrawalModel;
use App\Models\ReferralModel;
use App\Models\BankDetailsModel;
use App\Models\UserModel;

class Admin extends BaseController
{
    protected $adminModel;
    protected $withdrawalModel;
    protected $referralModel;
    protected $bankDetailsModel;
    protected $userModel;
    protected $session;

    public function __construct()
    {
        $this->adminModel = new AdminModel();
        $this->withdrawalModel = new WithdrawalModel();
        $this->referralModel = new ReferralModel();
        $this->bankDetailsModel = new BankDetailsModel();
        $this->userModel = new UserModel();
        $this->session = \Config\Services::session();
    }

    /**
     * Admin Dashboard
     */
    public function dashboard()
    {
        // Get dashboard statistics
        $withdrawalStats = $this->withdrawalModel->getWithdrawalStats();
        $referralStats = $this->referralModel->getReferralStats();
        $bankStats = $this->bankDetailsModel->getBankStats();

        // Get pending withdrawals count for sidebar
        $pendingCount = $this->withdrawalModel->where('status', 'pending')->countAllResults();

        // Get recent activities
        $recentWithdrawals = $this->withdrawalModel->getWithdrawalsWithDetails()
                                                  ->limit(5)
                                                  ->get()
                                                  ->getResultArray();

        $topAffiliates = $this->referralModel->getTopAffiliates(5);

        $data = [
            'title' => 'Admin Dashboard - Trade Diary',
            'page_title' => 'Dashboard',
            'page_subtitle' => 'Welcome to the admin panel',
            'active' => 'dashboard',
            'pending_count' => $pendingCount,
            'withdrawal_stats' => $withdrawalStats,
            'referral_stats' => $referralStats,
            'bank_stats' => $bankStats,
            'recent_withdrawals' => $recentWithdrawals,
            'top_affiliates' => $topAffiliates,
            'show_search' => true
        ];

        return $this->renderAdminView('admin/dashboard', $data);
    }

    /**
     * Payout Requests Management
     */
    public function payouts()
    {
        $data = [
            'title' => 'Payout Requests - Admin Panel',
            'page_title' => 'Payout Requests',
            'page_subtitle' => 'Manage affiliate withdrawal requests',
            'active' => 'payouts',
            'pending_count' => $this->withdrawalModel->where('status', 'pending')->countAllResults(),
            'show_search' => true,
            'custom_js' => 'payouts'
        ];

        return $this->renderAdminView('admin/payouts/index', $data);
    }

    /**
     * Get payout requests data (AJAX)
     */
    public function getPayoutRequests()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403)->setJSON(['error' => 'Forbidden']);
        }

        $filters = [
            'status' => $this->request->getGet('status'),
            'search' => $this->request->getGet('search'),
            'bank' => $this->request->getGet('bank'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to'),
            'amount_min' => $this->request->getGet('amount_min'),
            'amount_max' => $this->request->getGet('amount_max')
        ];

        $page = (int)($this->request->getGet('page') ?? 1);
        $perPage = (int)($this->request->getGet('per_page') ?? 10);
        $offset = ($page - 1) * $perPage;

        $builder = $this->withdrawalModel->getWithdrawalsWithDetails($filters);
        
        // Get total count
        $totalRecords = $builder->countAllResults(false);
        
        // Get paginated results
        $withdrawals = $builder->limit($perPage, $offset)->get()->getResultArray();

        // Format data for display
        $formattedWithdrawals = [];
        foreach ($withdrawals as $withdrawal) {
            $formattedWithdrawals[] = [
                'id' => $withdrawal['id'],
                'transaction_id' => $withdrawal['transaction_id'],
                'user' => [
                    'name' => $withdrawal['full_name'],
                    'email' => $withdrawal['email'],
                    'refer_code' => $withdrawal['refer_code'],
                    'wallet_balance' => $withdrawal['wallet_balance']
                ],
                'bank_details' => [
                    'account_name' => $withdrawal['account_name'],
                    'bank_name' => $withdrawal['bank_name'],
                    'account_number' => $this->bankDetailsModel->maskAccountNumber($withdrawal['account_number']),
                    'ifsc_code' => $withdrawal['ifsc_code'],
                    'branch_name' => $withdrawal['branch_name']
                ],
                'amount' => $withdrawal['amount'],
                'status' => $withdrawal['status'],
                'admin_notes' => $withdrawal['admin_notes'],
                'processed_by' => $withdrawal['processed_by'],
                'processed_at' => $withdrawal['processed_at'],
                'created_at' => $withdrawal['created_at'],
                'updated_at' => $withdrawal['updated_at']
            ];
        }

        return $this->response->setJSON([
            'success' => true,
            'data' => $formattedWithdrawals,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_records' => $totalRecords,
                'total_pages' => ceil($totalRecords / $perPage)
            ]
        ]);
    }

    /**
     * Approve withdrawal request
     */
    public function approveWithdrawal()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403)->setJSON(['error' => 'Forbidden']);
        }

        $withdrawalId = $this->request->getPost('withdrawal_id');
        $notes = $this->request->getPost('notes') ?? '';
        $adminId = $this->session->get('admin_id');

        if (!$withdrawalId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Withdrawal ID is required.'
            ]);
        }

        $result = $this->withdrawalModel->approveWithdrawal($withdrawalId, $adminId, $notes);

        return $this->response->setJSON($result);
    }

    /**
     * Reject withdrawal request
     */
    public function rejectWithdrawal()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403)->setJSON(['error' => 'Forbidden']);
        }

        $withdrawalId = $this->request->getPost('withdrawal_id');
        $notes = $this->request->getPost('notes') ?? '';
        $adminId = $this->session->get('admin_id');

        if (!$withdrawalId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Withdrawal ID is required.'
            ]);
        }

        if (empty($notes)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Rejection reason is required.'
            ]);
        }

        $result = $this->withdrawalModel->rejectWithdrawal($withdrawalId, $adminId, $notes);

        return $this->response->setJSON($result);
    }

    /**
     * Get recent notifications (AJAX)
     */
    public function getRecentNotifications()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403)->setJSON(['error' => 'Forbidden']);
        }

        try {
            // For now, return empty notifications since we don't have a notifications system yet
            // This can be expanded later when notification system is implemented
            $notifications = [];

            return $this->response->setJSON([
                'success' => true,
                'notifications' => $notifications,
                'count' => count($notifications)
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to load notifications'
            ]);
        }
    }

    /**
     * Mark notification as read (AJAX)
     */
    public function markNotificationAsRead($notificationId)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403)->setJSON(['error' => 'Forbidden']);
        }

        try {
            // For now, just return success since we don't have a notifications system yet
            // This can be expanded later when notification system is implemented

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Notification marked as read'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to mark notification as read'
            ]);
        }
    }

    /**
     * Get withdrawal details for modal
     */
    public function getWithdrawalDetails($withdrawalId)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403)->setJSON(['error' => 'Forbidden']);
        }

        $withdrawal = $this->withdrawalModel->getWithdrawalsWithDetails(['id' => $withdrawalId])
                                          ->where('w.id', $withdrawalId)
                                          ->get()
                                          ->getRowArray();

        if (!$withdrawal) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Withdrawal not found.'
            ]);
        }

        // Get affiliate performance
        $affiliatePerformance = $this->referralModel->getAffiliatePerformance($withdrawal['user_id']);

        return $this->response->setJSON([
            'success' => true,
            'data' => [
                'withdrawal' => $withdrawal,
                'affiliate_performance' => $affiliatePerformance
            ]
        ]);
    }

    /**
     * Export payout data to CSV
     */
    public function exportPayouts()
    {
        $filters = [
            'status' => $this->request->getGet('status'),
            'search' => $this->request->getGet('search'),
            'bank' => $this->request->getGet('bank'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to')
        ];

        $withdrawals = $this->withdrawalModel->getWithdrawalsWithDetails($filters)
                                           ->get()
                                           ->getResultArray();

        // Prepare CSV data
        $csvData = [];
        foreach ($withdrawals as $withdrawal) {
            $csvData[] = [
                'Transaction ID' => $withdrawal['transaction_id'],
                'User Name' => $withdrawal['full_name'],
                'Email' => $withdrawal['email'],
                'Affiliate Code' => $withdrawal['refer_code'],
                'Bank Name' => $withdrawal['bank_name'],
                'Account Number' => $withdrawal['account_number'],
                'IFSC Code' => $withdrawal['ifsc_code'],
                'Amount' => $withdrawal['amount'],
                'Status' => ucfirst($withdrawal['status']),
                'Request Date' => $withdrawal['created_at'],
                'Processed Date' => $withdrawal['processed_at'] ?? 'N/A',
                'Admin Notes' => $withdrawal['admin_notes'] ?? 'N/A'
            ];
        }

        // Set headers for CSV download
        $filename = 'payout_requests_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');

        $output = fopen('php://output', 'w');
        
        // Add CSV headers
        if (!empty($csvData)) {
            fputcsv($output, array_keys($csvData[0]));
            
            // Add data rows
            foreach ($csvData as $row) {
                fputcsv($output, $row);
            }
        }
        
        fclose($output);
        exit;
    }

    /**
     * Render admin view with common data
     */
    private function renderAdminView($view, $data = [])
    {
        // Add common admin data
        $commonData = [
            'admin_user' => [
                'id' => $this->session->get('admin_id'),
                'username' => $this->session->get('admin_username'),
                'full_name' => $this->session->get('admin_full_name'),
                'email' => $this->session->get('admin_email'),
                'role' => $this->session->get('admin_role'),
                'permissions' => $this->session->get('admin_permissions')
            ]
        ];

        $data = array_merge($commonData, $data);

        return view('admin/layouts/header', $data) .
               view('admin/layouts/sidebar', $data) .
               view('admin/layouts/topbar', $data) .
               view($view, $data) .
               view('admin/layouts/footer', $data);
    }
}
