<?php

namespace App\Models;

use CodeIgniter\Model;

class ReferralModel extends Model
{
    protected $table = 'referrals';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'referrer_id',
        'referred_user_id',
        'subscription_amount',
        'reward_percent',
        'reward_amount',
        'status'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'referrer_id' => 'required|integer',
        'referred_user_id' => 'required|integer',
        'subscription_amount' => 'required|decimal|greater_than[0]',
        'reward_percent' => 'required|integer|greater_than[0]|less_than_equal_to[100]',
        'reward_amount' => 'required|decimal|greater_than[0]'
    ];

    /**
     * Get referral statistics for admin dashboard
     */
    public function getReferralStats()
    {
        $stats = [];

        // Total referrals
        $stats['total_referrals'] = $this->countAllResults();

        // Total revenue generated
        $revenueResult = $this->selectSum('subscription_amount')->get()->getRowArray();
        $stats['total_revenue'] = $revenueResult['subscription_amount'] ?? 0;

        // Total rewards paid
        $rewardsResult = $this->selectSum('reward_amount')->get()->getRowArray();
        $stats['total_rewards'] = $rewardsResult['reward_amount'] ?? 0;

        // Today's referrals
        $todayReferrals = $this->where('DATE(created_at)', date('Y-m-d'))->countAllResults();
        $stats['today_referrals'] = $todayReferrals;

        // This month's referrals
        $thisMonthReferrals = $this->where('YEAR(created_at)', date('Y'))
                                  ->where('MONTH(created_at)', date('m'))
                                  ->countAllResults();
        $stats['this_month_referrals'] = $thisMonthReferrals;

        // This month's revenue
        $thisMonthRevenue = $this->where('YEAR(created_at)', date('Y'))
                                ->where('MONTH(created_at)', date('m'))
                                ->selectSum('subscription_amount')
                                ->get()
                                ->getRowArray();
        $stats['this_month_revenue'] = $thisMonthRevenue['subscription_amount'] ?? 0;

        // This month's rewards
        $thisMonthRewards = $this->where('YEAR(created_at)', date('Y'))
                                ->where('MONTH(created_at)', date('m'))
                                ->selectSum('reward_amount')
                                ->get()
                                ->getRowArray();
        $stats['this_month_rewards'] = $thisMonthRewards['reward_amount'] ?? 0;

        return $stats;
    }

    /**
     * Get top performing affiliates
     */
    public function getTopAffiliates($limit = 10)
    {
        return $this->db->table('referrals r')
            ->select('r.referrer_id, u.full_name, u.email, u.refer_code, 
                     COUNT(r.id) as total_referrals,
                     SUM(r.subscription_amount) as total_revenue,
                     SUM(r.reward_amount) as total_earnings,
                     u.wallet_balance')
            ->join('users u', 'r.referrer_id = u.id', 'left')
            ->groupBy('r.referrer_id')
            ->orderBy('total_earnings', 'DESC')
            ->limit($limit)
            ->get()
            ->getResultArray();
    }

    /**
     * Get referral details with user information
     */
    public function getReferralsWithDetails($filters = [])
    {
        $builder = $this->db->table('referrals r')
            ->select('r.*, 
                     referrer.full_name as referrer_name, 
                     referrer.email as referrer_email, 
                     referrer.refer_code as referrer_code,
                     referred.full_name as referred_name, 
                     referred.email as referred_email')
            ->join('users referrer', 'r.referrer_id = referrer.id', 'left')
            ->join('users referred', 'r.referred_user_id = referred.id', 'left');

        // Apply filters
        if (!empty($filters['search'])) {
            $builder->groupStart()
                ->like('referrer.full_name', $filters['search'])
                ->orLike('referrer.email', $filters['search'])
                ->orLike('referrer.refer_code', $filters['search'])
                ->orLike('referred.full_name', $filters['search'])
                ->orLike('referred.email', $filters['search'])
                ->groupEnd();
        }

        if (!empty($filters['date_from'])) {
            $builder->where('r.created_at >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $builder->where('r.created_at <=', $filters['date_to']);
        }

        if (!empty($filters['amount_min'])) {
            $builder->where('r.subscription_amount >=', $filters['amount_min']);
        }

        if (!empty($filters['amount_max'])) {
            $builder->where('r.subscription_amount <=', $filters['amount_max']);
        }

        return $builder->orderBy('r.created_at', 'DESC');
    }

    /**
     * Get affiliate performance metrics
     */
    public function getAffiliatePerformance($userId)
    {
        $performance = [];

        // Total referrals
        $performance['total_referrals'] = $this->where('referrer_id', $userId)->countAllResults();

        // Total earnings
        $earningsResult = $this->where('referrer_id', $userId)
                              ->selectSum('reward_amount')
                              ->get()
                              ->getRowArray();
        $performance['total_earnings'] = $earningsResult['reward_amount'] ?? 0;

        // Total revenue generated
        $revenueResult = $this->where('referrer_id', $userId)
                            ->selectSum('subscription_amount')
                            ->get()
                            ->getRowArray();
        $performance['total_revenue'] = $revenueResult['subscription_amount'] ?? 0;

        // Last 30 days performance
        $last30Days = $this->where('referrer_id', $userId)
                          ->where('created_at >=', date('Y-m-d', strtotime('-30 days')))
                          ->get()
                          ->getResultArray();

        $performance['last_30_days'] = [
            'referrals' => count($last30Days),
            'earnings' => array_sum(array_column($last30Days, 'reward_amount')),
            'revenue' => array_sum(array_column($last30Days, 'subscription_amount'))
        ];

        // Commission tier
        $totalReferrals = $performance['total_referrals'];
        if ($totalReferrals <= 50) {
            $performance['tier'] = 'Bronze';
            $performance['commission_rate'] = 10;
        } elseif ($totalReferrals <= 100) {
            $performance['tier'] = 'Silver';
            $performance['commission_rate'] = 15;
        } else {
            $performance['tier'] = 'Gold';
            $performance['commission_rate'] = 20;
        }

        return $performance;
    }

    /**
     * Get monthly referral report
     */
    public function getMonthlyReport($year, $month)
    {
        return $this->db->table('referrals r')
            ->select('r.*, 
                     referrer.full_name as referrer_name, 
                     referrer.email as referrer_email, 
                     referrer.refer_code as referrer_code,
                     referred.full_name as referred_name, 
                     referred.email as referred_email')
            ->join('users referrer', 'r.referrer_id = referrer.id', 'left')
            ->join('users referred', 'r.referred_user_id = referred.id', 'left')
            ->where('YEAR(r.created_at)', $year)
            ->where('MONTH(r.created_at)', $month)
            ->orderBy('r.created_at', 'DESC')
            ->get()
            ->getResultArray();
    }

    /**
     * Get referral trends (daily data for charts)
     */
    public function getReferralTrends($days = 30)
    {
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        
        return $this->db->table('referrals')
            ->select('DATE(created_at) as date, 
                     COUNT(*) as referrals,
                     SUM(subscription_amount) as revenue,
                     SUM(reward_amount) as rewards')
            ->where('created_at >=', $startDate)
            ->groupBy('DATE(created_at)')
            ->orderBy('date', 'ASC')
            ->get()
            ->getResultArray();
    }

    /**
     * Get commission distribution
     */
    public function getCommissionDistribution()
    {
        return $this->db->table('referrals')
            ->select('reward_percent, COUNT(*) as count, SUM(reward_amount) as total_amount')
            ->groupBy('reward_percent')
            ->orderBy('reward_percent', 'ASC')
            ->get()
            ->getResultArray();
    }
}
