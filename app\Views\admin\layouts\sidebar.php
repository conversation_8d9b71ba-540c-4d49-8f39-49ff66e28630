        <!-- Sidebar -->
        <div id="sidebar" class="sidebar-transition fixed inset-y-0 left-0 z-50 bg-gray-800 dark:bg-gray-900 text-white transform -translate-x-full lg:translate-x-0 lg:static lg:inset-0 transition-all duration-300"
             style="width: var(--sidebar-width, 16rem);">
            <!-- Sidebar Header -->
            <div class="flex items-center justify-between h-16 px-6 bg-gray-900 dark:bg-gray-800 border-b border-gray-700">
                <div class="flex items-center space-x-3" id="sidebarBrand">
                    <div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-chart-line text-white text-sm"></i>
                    </div>
                    <div class="sidebar-text">
                        <h1 class="text-lg font-bold whitespace-nowrap">Trade Diary</h1>
                        <p class="text-xs text-gray-400 whitespace-nowrap">Admin Panel</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <!-- Collapse Toggle (Desktop) -->
                    <button id="collapseSidebar" class="hidden lg:block text-gray-400 hover:text-white p-1 rounded transition-colors">
                        <i class="fas fa-angle-left text-sm"></i>
                    </button>
                    <!-- Close Button (Mobile) -->
                    <button id="closeSidebar" class="lg:hidden text-gray-400 hover:text-white p-1 rounded transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 mt-6 px-3 overflow-y-auto">
                <div class="space-y-1">
                    <!-- Dashboard -->
                    <a href="<?= base_url('admin/dashboard') ?>"
                       class="nav-link flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 group <?= ($active ?? '') === 'dashboard' ? 'bg-indigo-600 text-white shadow-lg' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>"
                       data-tooltip="Dashboard">
                        <i class="fas fa-tachometer-alt w-5 text-center flex-shrink-0"></i>
                        <span class="sidebar-text ml-3">Dashboard</span>
                    </a>

                    <!-- Payout Management -->
                    <div class="space-y-1">
                        <button class="sidebar-section-toggle w-full flex items-center justify-between px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider hover:text-gray-300 transition-colors"
                                data-section="payout">
                            <div class="flex items-center">
                                <i class="fas fa-money-bill-wave w-5 text-center flex-shrink-0"></i>
                                <span class="sidebar-text ml-2">Payout Management</span>
                            </div>
                            <i class="fas fa-chevron-down sidebar-text transition-transform duration-200" id="payout-chevron"></i>
                        </button>

                        <div class="sidebar-submenu space-y-1" id="payout-submenu">
                            <a href="<?= base_url('admin/payouts') ?>"
                               class="nav-link flex items-center px-3 py-2 ml-6 text-sm font-medium rounded-lg transition-all duration-200 group <?= ($active ?? '') === 'payouts' ? 'bg-indigo-600 text-white shadow-lg' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>"
                               data-tooltip="Payout Requests">
                                <i class="fas fa-credit-card w-5 text-center flex-shrink-0"></i>
                                <span class="sidebar-text ml-3">Payout Requests</span>
                                <?php if (isset($pending_count) && $pending_count > 0): ?>
                                    <span class="sidebar-text ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1"><?= $pending_count ?></span>
                                <?php endif; ?>
                            </a>

                            <a href="<?= base_url('admin/withdrawals') ?>"
                               class="nav-link flex items-center px-3 py-2 ml-6 text-sm font-medium rounded-lg transition-all duration-200 group <?= ($active ?? '') === 'withdrawals' ? 'bg-indigo-600 text-white shadow-lg' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>"
                               data-tooltip="Withdrawal History">
                                <i class="fas fa-history w-5 text-center flex-shrink-0"></i>
                                <span class="sidebar-text ml-3">Withdrawal History</span>
                            </a>
                        </div>
                    </div>

                    <!-- User Management -->
                    <div class="space-y-1">
                        <button class="sidebar-section-toggle w-full flex items-center justify-between px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider hover:text-gray-300 transition-colors"
                                data-section="users">
                            <div class="flex items-center">
                                <i class="fas fa-users w-5 text-center flex-shrink-0"></i>
                                <span class="sidebar-text ml-2">User Management</span>
                            </div>
                            <i class="fas fa-chevron-down sidebar-text transition-transform duration-200" id="users-chevron"></i>
                        </button>

                        <div class="sidebar-submenu space-y-1" id="users-submenu">
                            <a href="<?= base_url('admin/users') ?>"
                               class="nav-link flex items-center px-3 py-2 ml-6 text-sm font-medium rounded-lg transition-all duration-200 group <?= ($active ?? '') === 'users' ? 'bg-indigo-600 text-white shadow-lg' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>"
                               data-tooltip="All Users">
                                <i class="fas fa-user-friends w-5 text-center flex-shrink-0"></i>
                                <span class="sidebar-text ml-3">All Users</span>
                            </a>

                            <a href="<?= base_url('admin/affiliates') ?>"
                               class="nav-link flex items-center px-3 py-2 ml-6 text-sm font-medium rounded-lg transition-all duration-200 group <?= ($active ?? '') === 'affiliates' ? 'bg-indigo-600 text-white shadow-lg' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>"
                               data-tooltip="Affiliates">
                                <i class="fas fa-handshake w-5 text-center flex-shrink-0"></i>
                                <span class="sidebar-text ml-3">Affiliates</span>
                            </a>
                        </div>
                    </div>

                    <!-- Reports -->
                    <div class="space-y-1">
                        <button class="sidebar-section-toggle w-full flex items-center justify-between px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider hover:text-gray-300 transition-colors"
                                data-section="reports">
                            <div class="flex items-center">
                                <i class="fas fa-chart-bar w-5 text-center flex-shrink-0"></i>
                                <span class="sidebar-text ml-2">Reports & Analytics</span>
                            </div>
                            <i class="fas fa-chevron-down sidebar-text transition-transform duration-200" id="reports-chevron"></i>
                        </button>

                        <div class="sidebar-submenu space-y-1" id="reports-submenu">
                            <a href="<?= base_url('admin/reports/referrals') ?>"
                               class="nav-link flex items-center px-3 py-2 ml-6 text-sm font-medium rounded-lg transition-all duration-200 group <?= ($active ?? '') === 'referral-reports' ? 'bg-indigo-600 text-white shadow-lg' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>"
                               data-tooltip="Referral Reports">
                                <i class="fas fa-chart-pie w-5 text-center flex-shrink-0"></i>
                                <span class="sidebar-text ml-3">Referral Reports</span>
                            </a>

                            <a href="<?= base_url('admin/reports/financial') ?>"
                               class="nav-link flex items-center px-3 py-2 ml-6 text-sm font-medium rounded-lg transition-all duration-200 group <?= ($active ?? '') === 'financial-reports' ? 'bg-indigo-600 text-white shadow-lg' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>"
                               data-tooltip="Financial Reports">
                                <i class="fas fa-dollar-sign w-5 text-center flex-shrink-0"></i>
                                <span class="sidebar-text ml-3">Financial Reports</span>
                            </a>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="space-y-1">
                        <button class="sidebar-section-toggle w-full flex items-center justify-between px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider hover:text-gray-300 transition-colors"
                                data-section="settings">
                            <div class="flex items-center">
                                <i class="fas fa-cog w-5 text-center flex-shrink-0"></i>
                                <span class="sidebar-text ml-2">Settings</span>
                            </div>
                            <i class="fas fa-chevron-down sidebar-text transition-transform duration-200" id="settings-chevron"></i>
                        </button>

                        <div class="sidebar-submenu space-y-1" id="settings-submenu">
                            <a href="<?= base_url('admin/settings') ?>"
                               class="nav-link flex items-center px-3 py-2 ml-6 text-sm font-medium rounded-lg transition-all duration-200 group <?= ($active ?? '') === 'settings' ? 'bg-indigo-600 text-white shadow-lg' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>"
                               data-tooltip="System Settings">
                                <i class="fas fa-sliders-h w-5 text-center flex-shrink-0"></i>
                                <span class="sidebar-text ml-3">System Settings</span>
                            </a>

                            <a href="<?= base_url('admin/admins') ?>"
                               class="nav-link flex items-center px-3 py-2 ml-6 text-sm font-medium rounded-lg transition-all duration-200 group <?= ($active ?? '') === 'admins' ? 'bg-indigo-600 text-white shadow-lg' : 'text-gray-300 hover:bg-gray-700 hover:text-white' ?>"
                               data-tooltip="Admin Users">
                                <i class="fas fa-user-shield w-5 text-center flex-shrink-0"></i>
                                <span class="sidebar-text ml-3">Admin Users</span>
                            </a>
                        </div>
                    </div>

                    <!-- Dark Mode Toggle -->
                    <div class="mt-6 pt-4 border-t border-gray-700">
                        <button id="darkModeToggle"
                                class="w-full flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 text-gray-300 hover:bg-gray-700 hover:text-white group"
                                data-tooltip="Toggle Dark Mode">
                            <i class="fas fa-moon w-5 text-center flex-shrink-0" id="darkModeIcon"></i>
                            <span class="sidebar-text ml-3">Dark Mode</span>
                            <div class="sidebar-text ml-auto">
                                <div class="relative inline-block w-10 h-5 bg-gray-600 rounded-full transition-colors duration-200" id="darkModeSwitch">
                                    <div class="absolute left-0.5 top-0.5 w-4 h-4 bg-white rounded-full transition-transform duration-200" id="darkModeSwitchHandle"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="mt-auto p-4 bg-gray-900 dark:bg-gray-800 border-t border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-white text-sm font-medium">
                            <?= strtoupper(substr(session('admin_full_name') ?? 'A', 0, 1)) ?>
                        </span>
                    </div>
                    <div class="flex-1 min-w-0 sidebar-text">
                        <p class="text-sm font-medium text-white truncate">
                            <?= session('admin_full_name') ?? 'Admin User' ?>
                        </p>
                        <p class="text-xs text-gray-400 truncate">
                            <?= ucfirst(session('admin_role') ?? 'admin') ?>
                        </p>
                    </div>
                    <div class="relative">
                        <button id="userMenuButton" class="text-gray-400 hover:text-white p-2 rounded transition-colors">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div id="userMenu" class="hidden absolute bottom-full right-0 mb-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-1 z-50 border border-gray-200 dark:border-gray-600">
                            <a href="<?= base_url('admin/profile') ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                <i class="fas fa-user mr-2"></i>Profile
                            </a>
                            <a href="<?= base_url('admin/change-password') ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                <i class="fas fa-key mr-2"></i>Change Password
                            </a>
                            <hr class="my-1 border-gray-200 dark:border-gray-600">
                            <a href="<?= base_url('admin/logout') ?>" class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
