<!-- Admin Dashboard -->
<div class="space-y-6">
    <!-- Welcome Section -->
    <div class="admin-card rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold">Welcome back, <?= session('admin_full_name') ?>!</h1>
                <p class="text-indigo-100 mt-1">Here's what's happening with your affiliate program today.</p>
            </div>
            <div class="text-right">
                <div class="text-sm text-indigo-200">Last login</div>
                <div class="text-lg font-semibold"><?= date('M d, Y H:i') ?></div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Pending Withdrawals -->
        <div class="stats-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Pending Withdrawals</p>
                    <p class="text-3xl font-bold text-gray-900">
                        <?= $withdrawal_stats['pending']['count'] ?? 0 ?>
                    </p>
                    <p class="text-sm text-gray-500">
                        ₹<?= number_format($withdrawal_stats['pending']['amount'] ?? 0, 2) ?>
                    </p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Referrals -->
        <div class="stats-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Referrals</p>
                    <p class="text-3xl font-bold text-gray-900">
                        <?= $referral_stats['total_referrals'] ?? 0 ?>
                    </p>
                    <p class="text-sm text-green-600">
                        +<?= $referral_stats['today_referrals'] ?? 0 ?> today
                    </p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Revenue -->
        <div class="stats-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p class="text-3xl font-bold text-gray-900">
                        ₹<?= number_format($referral_stats['total_revenue'] ?? 0, 0) ?>
                    </p>
                    <p class="text-sm text-green-600">
                        ₹<?= number_format($referral_stats['this_month_revenue'] ?? 0, 0) ?> this month
                    </p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-line text-green-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Rewards Paid -->
        <div class="stats-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Rewards Paid</p>
                    <p class="text-3xl font-bold text-gray-900">
                        ₹<?= number_format($referral_stats['total_rewards'] ?? 0, 0) ?>
                    </p>
                    <p class="text-sm text-blue-600">
                        ₹<?= number_format($referral_stats['this_month_rewards'] ?? 0, 0) ?> this month
                    </p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-money-bill-wave text-purple-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Withdrawals -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Withdrawal Requests</h3>
                    <a href="<?= base_url('admin/payouts') ?>" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                        View all →
                    </a>
                </div>
            </div>
            <div class="divide-y divide-gray-200">
                <?php if (!empty($recent_withdrawals)): ?>
                    <?php foreach (array_slice($recent_withdrawals, 0, 5) as $withdrawal): ?>
                        <div class="p-6 hover:bg-gray-50 cursor-pointer" onclick="window.location.href='<?= base_url('admin/payouts') ?>'">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                                        <span class="text-indigo-600 font-medium text-sm">
                                            <?= strtoupper(substr($withdrawal['full_name'] ?? 'U', 0, 1)) ?>
                                        </span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900"><?= esc($withdrawal['full_name']) ?></p>
                                        <p class="text-sm text-gray-500"><?= esc($withdrawal['email']) ?></p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">₹<?= number_format($withdrawal['amount'], 2) ?></p>
                                    <p class="text-xs text-gray-500"><?= date('M d, Y', strtotime($withdrawal['created_at'])) ?></p>
                                    <?php
                                    $statusColors = [
                                        'pending' => 'bg-yellow-100 text-yellow-800',
                                        'approved' => 'bg-green-100 text-green-800',
                                        'rejected' => 'bg-red-100 text-red-800'
                                    ];
                                    $statusColor = $statusColors[$withdrawal['status']] ?? 'bg-gray-100 text-gray-800';
                                    ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?= $statusColor ?>">
                                        <?= ucfirst($withdrawal['status']) ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="p-6 text-center text-gray-500">
                        <i class="fas fa-inbox text-3xl mb-2"></i>
                        <p>No recent withdrawal requests</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Top Affiliates -->
        <div class="bg-white rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Top Affiliates</h3>
            </div>
            <div class="divide-y divide-gray-200">
                <?php if (!empty($top_affiliates)): ?>
                    <?php foreach (array_slice($top_affiliates, 0, 5) as $index => $affiliate): ?>
                        <div class="p-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                    <span class="text-indigo-600 font-medium text-xs">
                                        #<?= $index + 1 ?>
                                    </span>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        <?= esc($affiliate['full_name']) ?>
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        <?= $affiliate['total_referrals'] ?> referrals
                                    </p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">
                                        ₹<?= number_format($affiliate['total_earnings'], 0) ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="p-6 text-center text-gray-500">
                        <i class="fas fa-user-friends text-3xl mb-2"></i>
                        <p>No affiliate data available</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Action Buttons -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <a href="<?= base_url('admin/payouts') ?>" class="block">
            <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-credit-card text-yellow-600 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900">Review Payouts</h4>
                        <p class="text-sm text-gray-600">Process pending withdrawal requests</p>
                        <?php if ($pending_count > 0): ?>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1">
                                <?= $pending_count ?> pending
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </a>

        <a href="<?= base_url('admin/users') ?>" class="block">
            <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900">Manage Users</h4>
                        <p class="text-sm text-gray-600">View and manage user accounts</p>
                    </div>
                </div>
            </div>
        </a>

        <a href="<?= base_url('admin/reports/referrals') ?>" class="block">
            <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-bar text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900">View Reports</h4>
                        <p class="text-sm text-gray-600">Analyze affiliate performance</p>
                    </div>
                </div>
            </div>
        </a>
    </div>
</div>
