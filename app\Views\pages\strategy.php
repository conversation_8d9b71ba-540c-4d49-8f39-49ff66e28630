<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">

    <div class="flex justify-between items-center mb-6">
        <div>

        </div>
        <div class="flex space-x-3">
            <div class="relative">
                <select id="marketTypeFilter"
                    class="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-all focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                    <option value="1">Indian</option>
                    <option value="2">Forex</option>
                    <option value="3">Crypto</option>
                </select>
            </div>
            <div class="relative">
                <select id="strategyFilter"
                    class="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-all focus:outline-none focus:ring-2 focus:ring-blue-light dark:focus:ring-blue-dark">
                    <option value="1">Last 30 Days</option>
                    <option value="3">Last 90 Days</option>
                    <option value="12">Year to Date</option>
                </select>
            </div>
            <button id="addStrategyBtn"
                class="px-4 py-2 bg-blue-500 dark:bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-dark dark:hover:bg-blue-light transition-all shadow hover:shadow-lg">
                <i class="fas fa-plus mr-2"></i> New Strategy
            </button>
        </div>
    </div>

    <!-- Card View -->
    <div id="cardView" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div class="col-span-3 text-center py-10">
            <div class="flex justify-center mb-4">
                <i class="fas fa-chart-line text-4xl text-gray-400"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-200">No Strategies Found</h3>
            <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">
                Try a different time range or start recording your trades.
            </p>
        </div>

    </div>

    <!-- Recent Trades -->
    <div class="bg-white dark:bg-dark-800 rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Recent Trades</h2>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Strategy</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Symbol</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Result</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr>
                        <td colspan="5" class="text-center py-4 text-gray-500">No recent trades found.</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="px-6 py-4 flex justify-end">
            <div id="tradePagination" class="inline-flex items-center space-x-1"></div>
        </div>
    </div>
</div>


<!-- Modern Strategy Details Modal -->
<div id="strategyModal" class="modal fixed inset-0 z-50 overflow-y-auto" style="display: none;">
    <div class="flex items-center justify-center min-h-screen px-4 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-900/70 backdrop-blur-sm"></div>
        </div>

        <div class="modal-content inline-block w-full max-w-md my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-dark-800 rounded-xl shadow-2xl relative">
            <!-- Close Button -->
            <button type="button" class="close-modal absolute top-4 right-4 z-10 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <!-- Modal Header -->
            <div class="px-6 pt-6 pb-4 border-b border-gray-100 dark:border-gray-700">
                <div class="flex items-center space-x-4">
                    <div id="strategyIcon" class="flex-shrink-0 h-12 w-12 rounded-lg bg-indigo-100 dark:bg-indigo-900/50 flex items-center justify-center">
                        <i class="fas fa-exchange-alt text-indigo-600 dark:text-indigo-300 text-xl"></i>
                    </div>
                    <div>
                        <h3 id="strategyName" class="text-xl font-bold text-gray-900 dark:text-gray-100">EMA Crossover</h3>
                        <p id="strategyType" class="text-gray-500 dark:text-gray-400">Swing Trading</p>
                    </div>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="px-6 py-4">
                <!-- Performance Metrics -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                        <p class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wider">Win Rate</p>
                        <div class="flex items-center mt-1">
                            <span id="winRate" class="text-xl font-bold text-gray-900 dark:text-white mr-2">72%</span>
                            <div class="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                <div id="winRateBar" class="bg-green-500 h-2 rounded-full" style="width: 72%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                        <p class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wider">Profit Factor</p>
                        <p id="profitFactor" class="text-xl font-bold text-gray-900 dark:text-white mt-1">1.8</p>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                        <p class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wider">Risk/Trade</p>
                        <p id="riskPerTrade" class="text-xl font-bold text-gray-900 dark:text-white mt-1">1.5%</p>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                        <p class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wider">Total Profit</p>
                        <p id="totalProfit" class="text-xl font-bold text-green-500 mt-1">+$8,742</p>
                    </div>
                </div>

                <!-- Description -->
                <div class="mb-6">
                    <h4 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">Description</h4>
                    <p id="strategyDescription" class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                        This strategy uses exponential moving averages (EMA) to identify trend reversals.
                        It enters long positions when the shorter EMA crosses above the longer EMA and
                        exits when the opposite crossover occurs. Works best in trending markets.
                    </p>
                </div>

                <!-- Recent Trades -->
                <div>
                    <h4 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">Recent Trades</h4>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">BTC/USD</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Long • 2h ago</p>
                            </div>
                            <span class="text-sm font-semibold text-green-500">+2.33%</span>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">ETH/USD</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Short • 5h ago</p>
                            </div>
                            <span class="text-sm font-semibold text-green-500">+1.63%</span>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">AAPL</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">Long • Yesterday</p>
                            </div>
                            <span class="text-sm font-semibold text-green-500">+0.93%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="px-6 py-4 border-t border-gray-100 dark:border-gray-700 flex justify-end">
                <button type="button" class="close-modal px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>


<!-- Modal Backdrop -->
<div id="modal-backdrop" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden modal-backdrop"></div>

<!-- <div id="strategy-modal" class="fixed inset-0 z-50 popup-overlay hidden items-center justify-center p-4">
    <div class="popup-content bg-gray-800 rounded-xl max-w-md w-full p-6 shadow-2xl border border-gray-700">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-bold text-white">Add New Strategy</h3>
            <button onclick="closeStratModal()" id="closePopup" class="text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form class="space-y-4">
            <div>
                <label for="strategyNameInput" class="block text-sm font-medium text-gray-300 mb-1">Name</label>
                <input type="text" id="strategyNameInput" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="Enter strategy name">
            </div>

            <div>
                <label for="strategyDesc" class="block text-sm font-medium text-gray-300 mb-1">Description</label>
                <textarea id="strategyDesc" rows="3" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="Enter strategy description"></textarea>
            </div>

            <div class="flex justify-end gap-3 pt-2">
                <button onclick="closeStratModal()" type="button" id="cancelBtn" class="px-4 py-2 text-gray-300 hover:text-white rounded-lg transition-colors">Cancel</button>
                <button onclick="saveUserStrategy('<?= base_url('saveUserStrategy') ?>')" type="button" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors font-medium">Save Strategy</button>
            </div>
        </form>
    </div>
</div> -->

<div id="strategy-modal" class="fixed inset-0 z-50 overflow-y-auto hidden">
    <div class="flex items-center justify-center min-h-screen px-4 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-900/70 backdrop-blur-sm"></div>
        </div>

        <div class="modal-content inline-block w-full max-w-md my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-dark-800 rounded-xl shadow-2xl relative">
            <!-- Close Button -->
            <button onclick="closeStratModal()" type="button" class="close-add-modal absolute top-4 right-4 z-10 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <!-- Modal Header -->
            <div class="px-6 pt-6 pb-4 border-b border-gray-100 dark:border-gray-700">
                <h3 class="text-xl font-bold text-gray-900 dark:text-gray-100">Add New Strategy</h3>
            </div>

            <!-- Modal Body -->
            <div class="px-6 py-4">
                <form id="addStrategyForm">
                    <div class="mb-4">
                        <label for="strategyNameInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Strategy Name</label>
                        <input type="text" id="strategyNameInput" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Enter strategy name">
                    </div>

                    <div class="mb-4">
                        <label for="strategyDesc" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
                        <textarea id="strategyDesc" rows="4" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Enter strategy description"></textarea>
                    </div>

                    <div class="mt-6">
                        <button onclick="saveUserStrategy('<?= base_url('saveUserStrategy') ?>')" type="button" class="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition">
                            Add Strategy
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Edit Strategy Modal -->
<div id="editStrategyModal" class="fixed inset-0 z-50 overflow-y-auto"  style="display: none;">
    <div class="flex items-center justify-center min-h-screen px-4 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-900/70 backdrop-blur-sm"></div>
        </div>

        <div class="modal-content inline-block w-full max-w-md my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-dark-800 rounded-xl shadow-2xl relative">
            <!-- Close Button -->
            <button onclick="closeEditModal()" type="button" class="close-edit-modal absolute top-4 right-4 z-10 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <!-- Modal Header -->
            <div class="px-6 pt-6 pb-4 border-b border-gray-100 dark:border-gray-700">
                <h3 class="text-xl font-bold text-gray-900 dark:text-gray-100">Edit Strategy</h3>
            </div>

            <!-- Modal Body -->
            <div class="px-6 py-4">
                <form id="editStrategyForm">
                    <div class="mb-4">
                        <label for="editStrategyName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Strategy Name</label>
                        <input type="text" id="editStrategyName" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-dark-800 dark:text-gray-200">
                    </div>

                    <div class="mb-4">
                        <label for="editStrategyDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
                        <textarea id="editStrategyDescription" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-dark-800 dark:text-gray-200"></textarea>
                    </div>
                </form>
            </div>

            <!-- Modal Footer -->
            <div class="px-6 py-4 border-t border-gray-100 dark:border-gray-700 flex justify-end space-x-3">
                <button onclick="closeEditModal()" type="button" class="close-edit-modal px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Cancel
                </button>
                <button onclick="updateStrategy('<?= base_url('updateStrategy') ?>')" type="button" id="saveStrategyBtn" class="px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Save Changes
                </button>
            </div>
        </div>
    </div>
</div>