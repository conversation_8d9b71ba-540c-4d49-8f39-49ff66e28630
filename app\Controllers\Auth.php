<?php

namespace App\Controllers;

use App\Models\UserModel;
use Google_Client;
use Google_Service_Oauth2;

helper('cookie');

class Auth extends BaseController
{
    public function __construct()
    {
        $this->usermodel = new UserModel();
    }

    public function googleLogin()
    {
        $client = new Google_Client();
        $client->setClientId('564203561371-sj66237nskju41k964p5i2c1u8i86jpq.apps.googleusercontent.com');
        $client->setClientSecret('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');
        $client->setRedirectUri(base_url('googleCallback'));
        $client->addScope('email');
        $client->addScope('profile');

        return redirect()->to($client->createAuthUrl());
    }

    public function googleCallback()
    {
        $db = \Config\Database::connect();

        $client = new \Google_Client();
        $client->setClientId('564203561371-sj66237nskju41k964p5i2c1u8i86jpq.apps.googleusercontent.com');
        $client->setClientSecret('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');
        $client->setRedirectUri(base_url('googleCallback'));

        if ($this->request->getVar('code')) {
            $token = $client->fetchAccessTokenWithAuthCode($this->request->getVar('code'));
            $client->setAccessToken($token['access_token']);

            $googleService = new \Google_Service_Oauth2($client);
            $userData = $googleService->userinfo->get();

            $userModel = new \App\Models\UserModel();
            $user = $userModel->where('email', $userData->email)->first();

            $isNewUser = false;

            if ($user) {
                $userId = $user['id'];
            } else {
                // New user
                $isNewUser = true;

                // Check if 'payment_id' cookie exists
                if (!$this->request->getCookie('osrgdthfudtrshf')) {
                    return redirect()->to(base_url() . '#pricing')->with('error', 'Payment not completed. Please complete payment to proceed.');
                }
                $payment_id = $this->request->getCookie('osrgdthfudtrshf');

                $paymentDetails = $db->query("SELECT * FROM transactions WHERE payment_id = '$payment_id' ")->getRowArray();

                $period = $paymentDetails['period'];

                $sub_start = date('Y-m-d H:i:s');

                if ($period == 'monthly') {
                    $sub_end = date('Y-m-d 23:59:59', strtotime('+1 month'));
                } elseif ($period == 'annual') {
                    $sub_end = date('Y-m-d 23:59:59', strtotime('+1 year'));
                } else {
                    // Default/fallback if period is not recognized
                    $sub_end = date('Y-m-d 23:59:59', strtotime('+1 month'));
                }

                $name = $userData->name;

                $referralCode = $this->generateReferralCode($name);

                // Insert user
                $userModel->insert([
                    'full_name' => $userData->name,
                    'email' => $userData->email,
                    'refer_code' => $referralCode,
                    'google_id' => $userData->id,
                    'profile' => $userData->picture,
                    'sub_start' => $sub_start,
                    'sub_end' => $sub_end,
                    'payment_id' => $payment_id,
                    'is_verified' => 1
                ]);
                $userId = $userModel->insertID();
            }
            $this->response->deleteCookie('osrgdthfudtrshf');

            // Encrypt user ID
            $encryptedId = $this->encrypt_cookie_value($userId);

            if ($encryptedId !== false) {
                $response = service('response');

                $response->setCookie([
                    'name' => 'user_session',
                    'value' => $encryptedId,
                    'expire' => 60 * 60 * 24 * 30, // 30 days
                    'secure' => false, // set true for HTTPS
                    'httponly' => true,
                    'path' => '/',
                    'samesite' => 'Lax',
                ]);

                return $response->redirect(base_url('dashboard'));
            }

            // Fallback if encryption fails
            return redirect()->to('/')->with('error', 'Failed to login, try again.');
        }

        return redirect()->to('/');
    }

    private function generateReferralCode($name)
    {
        // Clean and lowercase the name
        $cleanName = strtolower(preg_replace('/[^a-zA-Z]/', '', $name));

        // Pad the name with "x" if less than 4 characters
        $prefix = str_pad(substr($cleanName, 0, 4), 4, 'x');

        // Generate a random 4-digit number
        $randomNumber = rand(1000, 9999);

        // Combine and return uppercase code
        return strtoupper($prefix . $randomNumber); // e.g., ALEx5732 or JOxX2183
    }

    public function MyProfile()
    {
        $authCheck = $this->checkAuthentication();
        if ($authCheck !== true) {
            return $authCheck;
        }

        $db = \Config\Database::connect();

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data['title'] = 'My Profile';
        $data['active'] = 'dashboard';
        $data['userDetails'] = $this->usermodel->find($userId);
        $data['customScript'] = 'profile';
        $data['main_content'] = 'pages/profile';

        return view('includes/template', $data);
    }

    public function updateProfile()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request type'
            ]);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $name = $this->request->getPost('name');
        $password = $this->request->getPost('password');
        $confirmPassword = $this->request->getPost('confirm_password');

        $validation = \Config\Services::validation();
        $validation->setRules([
            'name' => 'required|min_length[2]',
            'password' => 'permit_empty|min_length[8]',
            'confirm_password' => 'matches[password]',
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validation->getErrors()
            ]);
        }

        $updateData = [
            'full_name' => $name
        ];

        if (!empty($password)) {
            $updateData['password'] = password_hash($password, PASSWORD_BCRYPT);
        }

        $this->usermodel->update($userId, $updateData);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Profile updated successfully'
        ]);
    }

    public function saveBrokerDetails()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request type'
            ]);
        }

        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $broker = trim($this->request->getPost('broker'));
        $clientId = trim($this->request->getPost('clientID'));
        $accessToken = trim($this->request->getPost('accessToken'));

        // Validate input
        if (empty($clientId) || empty($accessToken)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Client ID and Access Token are required.'
            ]);
        }

        // Prepare data
        $data = [
            'broker' => $broker,
            'client_id' => $clientId,
            'connected_on' => date('Y-m-d H:i:s'),
            'access_token' => $accessToken
        ];

        try {
            if (!$this->usermodel->update($userId, $data)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update broker details.',
                    'errors' => $this->usermodel->errors() // if using CI4 model validation
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Broker update failed for user ' . $userId . ': ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An unexpected error occurred while saving broker details.'
            ]);
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Broker configured successfully.'
        ]);
    }

    public function fetchConnectedBroker()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        if (!$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $details = $this->usermodel->find($userId);

        if (!$details || empty($details['broker']) || empty($details['client_id'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No broker connected'
            ]);
        }

        // Optional: format connected_on date
        $details['connected_on'] = date('Y-m-d H:i:s', strtotime($details['updated_at'] ?? $details['created_at'] ?? 'now'));

        // Return only needed fields
        return $this->response->setJSON([
            'success' => true,
            'data' => [
                'broker' => $details['broker'],
                'client_id' => $details['client_id'],
                'access_token' => $details['access_token'],
                'connected_on' => $details['connected_on'],
                'sub_start' => $details['sub_start'] ?? null,
                'sub_end' => $details['sub_end'] ?? null
            ]
        ]);
    }

    public function deleteBroker()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session'));

        $data = [
            'broker' => null,
            'client_id' => null,
            'connected_on' => null,
            'access_token' => null
        ];

        $updated = $this->usermodel->update($userId, $data);

        if ($updated) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Broker connection removed successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to remove broker connection'
            ]);
        }
    }

    public function syncTrades()
    {
        $userId = $this->decrypt_cookie_value(get_cookie('user_session')); // or wherever you store it

        if (!$userId) {
            return $this->response->setJSON(['success' => false, 'message' => 'User not authenticated']);
        }

        $userDetails = $this->usermodel->find($userId);

        $broker = $userDetails['broker'];

        if ($broker == null) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You have not connected any broker yet!',
            ]);
        } elseif ($broker == 'dhan') {

            $scriptPath = '/home/<USER>/htdocs/tradediary.in/python/dhan_connect.py';
            $pythonPath = '/home/<USER>/htdocs/tradediary.in/python/venv/bin/python';

            $command = escapeshellcmd("$pythonPath $scriptPath $userId");
            $output = shell_exec($command);

            $decoded = json_decode($output, true);

            if (!is_array($decoded)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid response from broker script.',
                ]);
            }

            if (!isset($decoded['data']) || empty($decoded['data'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'No new trades found to sync.',
                ]);
            }

            if ($this->saveDhanData($decoded, $userId)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Trades have been fetched and saved successfully.',
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to save trades. Please verify the broker details added or update the details.',
                ]);
            }
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request!',
            ]);
        }
    }

    public function saveDhanData($orders, $userId)
    {
        $db = \Config\Database::connect();
        if (!isset($orders['status']) || $orders['status'] !== 'success' || !isset($orders['data']) || empty($orders['data'])) {
            return false;
        }

        $builder = $db->table('trades');

        // Filter only TRADED orders
        $tradedOrders = array_filter($orders['data'], fn($o) => $o['orderStatus'] === 'TRADED');

        // Group by tradingSymbol and transactionType (buy/sell)
        $groupedOrders = [];
        foreach ($tradedOrders as $order) {
            $symbol = $order['tradingSymbol'];
            $side = strtolower($order['transactionType']); // buy or sell
            $groupedOrders[$symbol][$side][] = $order;
        }

        foreach ($groupedOrders as $symbol => $sides) {
            $buyOrders = $sides['buy'] ?? [];
            $sellOrders = $sides['sell'] ?? [];

            $minCount = min(count($buyOrders), count($sellOrders));

            for ($i = 0; $i < $minCount; $i++) {
                $buy = $buyOrders[$i];
                $sell = $sellOrders[$i];

                $quantity = min($buy['filledQty'], $sell['filledQty']);
                if ($quantity == 0)
                    continue;

                $entryPrice = $buy['averageTradedPrice'];
                $exitPrice = $sell['averageTradedPrice'];
                $entryAmount = $entryPrice * $quantity;
                $pnlAmount = ($exitPrice - $entryPrice) * $quantity;
                $pnlPercent = $entryAmount > 0 ? round(($pnlAmount / $entryAmount) * 100, 2) : 0;

                $datetime = date('Y-m-d', strtotime($sell['exchangeTime']));
                $orderId = $sell['orderId']; // use SELL order ID as unique identifier

                // Avoid duplicates using unique broker_order_id
                $exists = $builder->where('broker_order_id', $orderId)->countAllResults();
                if ($exists > 0)
                    continue;

                // Insert into trades table
                $builder->insert([
                    'user_id' => $userId,
                    'symbol' => $symbol,
                    'datetime' => $datetime,
                    'entry_price' => $entryPrice,
                    'entry_quantity' => $quantity,
                    'entry_amount' => $entryAmount,
                    'exit_price' => $exitPrice,
                    'pnl_amount' => $pnlAmount,
                    'pnl_percent' => $pnlPercent,
                    'trade_type' => 1,
                    'broker' => 'dhan',
                    'broker_order_id' => $orderId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            }
        }

        return true;
    }
}
