            </main>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Common Admin Scripts -->
    <script>
        $(document).ready(function() {
            // Sidebar Toggle
            $('#openSidebar').click(function() {
                $('#sidebar').removeClass('-translate-x-full');
                $('#sidebarOverlay').removeClass('hidden');
            });

            $('#closeSidebar, #sidebarOverlay').click(function() {
                $('#sidebar').addClass('-translate-x-full');
                $('#sidebarOverlay').addClass('hidden');
            });

            // Dropdown Toggles
            $('#userMenuButton').click(function(e) {
                e.stopPropagation();
                $('#userMenu').toggleClass('hidden');
            });

            $('#notificationButton').click(function(e) {
                e.stopPropagation();
                $('#notificationDropdown').toggleClass('hidden');
                loadNotifications();
            });

            $('#quickActionsButton').click(function(e) {
                e.stopPropagation();
                $('#quickActionsDropdown').toggleClass('hidden');
            });

            $('#profileButton').click(function(e) {
                e.stopPropagation();
                $('#profileDropdown').toggleClass('hidden');
            });

            // Close dropdowns when clicking outside
            $(document).click(function() {
                $('#userMenu, #notificationDropdown, #quickActionsDropdown, #profileDropdown').addClass('hidden');
            });

            // Prevent dropdown close when clicking inside
            $('#userMenu, #notificationDropdown, #quickActionsDropdown, #profileDropdown').click(function(e) {
                e.stopPropagation();
            });

            // Auto-refresh notifications every 30 seconds
            setInterval(loadNotifications, 30000);

            // Initialize tooltips
            initializeTooltips();

            // Initialize dark mode toggle
            $('#themeToggle').click(function() {
                if (typeof DarkMode !== 'undefined') {
                    DarkMode.toggleTheme();
                }
            });

            // Apply dark mode to dynamically loaded content
            $(document).ajaxComplete(function() {
                if (typeof DarkMode !== 'undefined') {
                    DarkMode.applyThemeToElements();
                }
            });

            // Initialize CSRF token for AJAX requests
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                        xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
                        xhr.setRequestHeader("X-CSRF-TOKEN", CSRF_HASH);
                    }
                }
            });
        });

        // Toast Notification System
        function showToast(message, type = 'info', duration = 5000) {
            const toastId = 'toast_' + Date.now();
            const toastHtml = `
                <div id="${toastId}" class="toast toast-${type}">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas ${getToastIcon(type)} mr-2"></i>
                            <span>${message}</span>
                        </div>
                        <button onclick="removeToast('${toastId}')" class="ml-4 text-white hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
            
            $('#toastContainer').append(toastHtml);
            
            // Show toast
            setTimeout(() => {
                $(`#${toastId}`).addClass('show');
            }, 100);
            
            // Auto remove
            setTimeout(() => {
                removeToast(toastId);
            }, duration);
        }

        function getToastIcon(type) {
            const icons = {
                'success': 'fa-check-circle',
                'error': 'fa-exclamation-circle',
                'warning': 'fa-exclamation-triangle',
                'info': 'fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        function removeToast(toastId) {
            const toast = $(`#${toastId}`);
            toast.removeClass('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }

        // Loading Overlay
        function showLoading() {
            $('#loadingOverlay').removeClass('hidden').addClass('flex');
        }

        function hideLoading() {
            $('#loadingOverlay').addClass('hidden').removeClass('flex');
        }

        // Load Notifications
        function loadNotifications() {
            $.ajax({
                url: ADMIN_BASE_URL + '/notifications/recent',
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        updateNotificationList(response.notifications);
                        updateNotificationCount(response.count);
                    }
                },
                error: function() {
                    console.error('Failed to load notifications');
                }
            });
        }

        function updateNotificationList(notifications) {
            const container = $('#notificationList');
            container.empty();
            
            if (notifications.length === 0) {
                container.html(`
                    <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                        <i class="fas fa-bell-slash text-2xl mb-2"></i>
                        <p>No new notifications</p>
                    </div>
                `);
                return;
            }
            
            notifications.forEach(notification => {
                const notificationHtml = `
                    <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors" onclick="markAsRead(${notification.id})">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas ${notification.icon} text-${notification.color}-500"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">${notification.title}</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">${notification.message}</p>
                                <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">${notification.time_ago}</p>
                            </div>
                        </div>
                    </div>
                `;
                container.append(notificationHtml);
            });
        }

        function updateNotificationCount(count) {
            const badge = $('#notificationButton .absolute');
            if (count > 0) {
                badge.text(count > 9 ? '9+' : count).removeClass('hidden');
            } else {
                badge.addClass('hidden');
            }
        }

        function markAsRead(notificationId) {
            $.ajax({
                url: ADMIN_BASE_URL + '/notifications/mark-read/' + notificationId,
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        loadNotifications();
                    }
                }
            });
        }

        // Initialize Tooltips
        function initializeTooltips() {
            $('[data-tooltip]').each(function() {
                const element = $(this);
                const tooltip = element.attr('data-tooltip');
                
                element.hover(
                    function() {
                        const tooltipElement = $(`<div class="tooltip">${tooltip}</div>`);
                        $('body').append(tooltipElement);
                        
                        const rect = this.getBoundingClientRect();
                        tooltipElement.css({
                            position: 'absolute',
                            top: rect.top - tooltipElement.outerHeight() - 5,
                            left: rect.left + (rect.width / 2) - (tooltipElement.outerWidth() / 2),
                            zIndex: 9999
                        });
                    },
                    function() {
                        $('.tooltip').remove();
                    }
                );
            });
        }

        // Confirm Dialog
        function confirmAction(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }

        // Format Currency
        function formatCurrency(amount) {
            return '₹' + parseFloat(amount).toLocaleString('en-IN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        // Format Date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Export to CSV
        function exportToCSV(data, filename) {
            const csv = convertToCSV(data);
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            window.URL.revokeObjectURL(url);
        }

        function convertToCSV(data) {
            if (!data.length) return '';
            
            const headers = Object.keys(data[0]);
            const csvHeaders = headers.join(',');
            const csvRows = data.map(row => 
                headers.map(header => {
                    const value = row[header];
                    return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                }).join(',')
            );
            
            return [csvHeaders, ...csvRows].join('\n');
        }
    </script>

    <!-- Page-specific scripts -->
    <?php if (isset($custom_js)): ?>
        <script src="<?= base_url('assets/admin/js/' . $custom_js . '.js?v=' . time()) ?>"></script>
    <?php endif; ?>

    <?php if (isset($inline_js)): ?>
        <script><?= $inline_js ?></script>
    <?php endif; ?>

</body>
</html>
