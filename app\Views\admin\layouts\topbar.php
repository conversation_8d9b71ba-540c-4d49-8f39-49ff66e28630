        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between h-16 px-6">
                    <!-- Left Side -->
                    <div class="flex items-center space-x-4">
                        <!-- Mobile Menu Button -->
                        <button id="openSidebar" class="lg:hidden text-gray-500 hover:text-gray-700">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        
                        <!-- Page Title -->
                        <div>
                            <h1 class="text-xl font-semibold text-gray-900">
                                <?= $page_title ?? 'Admin Dashboard' ?>
                            </h1>
                            <?php if (isset($page_subtitle)): ?>
                                <p class="text-sm text-gray-500"><?= $page_subtitle ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Right Side -->
                    <div class="flex items-center space-x-4">
                        <!-- Search Bar (Optional) -->
                        <?php if (isset($show_search) && $show_search): ?>
                            <div class="hidden md:block">
                                <div class="relative">
                                    <input type="text" 
                                           id="globalSearch"
                                           placeholder="Search..." 
                                           class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-search text-gray-400"></i>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Notifications -->
                        <div class="relative">
                            <button id="notificationButton" class="relative p-2 text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-lg">
                                <i class="fas fa-bell text-xl"></i>
                                <?php if (isset($notification_count) && $notification_count > 0): ?>
                                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                        <?= $notification_count > 9 ? '9+' : $notification_count ?>
                                    </span>
                                <?php endif; ?>
                            </button>
                            
                            <!-- Notification Dropdown -->
                            <div id="notificationDropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                <div class="p-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
                                </div>
                                <div class="max-h-64 overflow-y-auto">
                                    <div id="notificationList" class="divide-y divide-gray-200">
                                        <!-- Notifications will be loaded here -->
                                        <div class="p-4 text-center text-gray-500">
                                            <i class="fas fa-bell-slash text-2xl mb-2"></i>
                                            <p>No new notifications</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-3 border-t border-gray-200">
                                    <a href="<?= base_url('admin/notifications') ?>" class="block text-center text-sm text-indigo-600 hover:text-indigo-800">
                                        View all notifications
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="relative">
                            <button id="quickActionsButton" class="p-2 text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-lg">
                                <i class="fas fa-plus text-xl"></i>
                            </button>
                            
                            <!-- Quick Actions Dropdown -->
                            <div id="quickActionsDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                <div class="py-1">
                                    <a href="<?= base_url('admin/payouts') ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-credit-card mr-2"></i>Review Payouts
                                    </a>
                                    <a href="<?= base_url('admin/users/create') ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user-plus mr-2"></i>Add User
                                    </a>
                                    <a href="<?= base_url('admin/reports/export') ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-download mr-2"></i>Export Data
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- User Profile -->
                        <div class="relative">
                            <button id="profileButton" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <div class="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">
                                        <?= strtoupper(substr(session('admin_full_name') ?? 'A', 0, 1)) ?>
                                    </span>
                                </div>
                                <div class="hidden md:block text-left">
                                    <p class="text-sm font-medium text-gray-900">
                                        <?= session('admin_full_name') ?? 'Admin User' ?>
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        <?= ucfirst(session('admin_role') ?? 'admin') ?>
                                    </p>
                                </div>
                                <i class="fas fa-chevron-down text-gray-400 text-sm"></i>
                            </button>
                            
                            <!-- Profile Dropdown -->
                            <div id="profileDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                <div class="py-1">
                                    <div class="px-4 py-2 border-b border-gray-200">
                                        <p class="text-sm font-medium text-gray-900">
                                            <?= session('admin_full_name') ?? 'Admin User' ?>
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            <?= session('admin_email') ?? '<EMAIL>' ?>
                                        </p>
                                    </div>
                                    <a href="<?= base_url('admin/profile') ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user mr-2"></i>Profile Settings
                                    </a>
                                    <a href="<?= base_url('admin/change-password') ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-key mr-2"></i>Change Password
                                    </a>
                                    <a href="<?= base_url('admin/activity-log') ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-history mr-2"></i>Activity Log
                                    </a>
                                    <hr class="my-1">
                                    <a href="<?= base_url('admin/logout') ?>" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                        <i class="fas fa-sign-out-alt mr-2"></i>Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
