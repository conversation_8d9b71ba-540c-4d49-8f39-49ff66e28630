<!-- Payout Requests Management -->
<div class="space-y-6">
    <!-- Header Section -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Payout Requests</h2>
                <p class="text-gray-600 mt-1">Manage affiliate withdrawal requests and process payouts</p>
            </div>
            <div class="flex items-center space-x-3">
                <button id="refreshData" class="btn-primary px-4 py-2 rounded-lg text-white font-medium hover:opacity-90 transition-opacity">
                    <i class="fas fa-sync-alt mr-2"></i>Refresh
                </button>
                <button id="exportCSV" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-download mr-2"></i>Export CSV
                </button>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Search -->
            <div>
                <label for="searchFilter" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <div class="relative">
                    <input type="text" 
                           id="searchFilter" 
                           placeholder="Search by name, email, code..."
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Status Filter -->
            <div>
                <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                </select>
            </div>

            <!-- Bank Filter -->
            <div>
                <label for="bankFilter" class="block text-sm font-medium text-gray-700 mb-2">Bank</label>
                <select id="bankFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    <option value="">All Banks</option>
                    <option value="SBI">State Bank of India</option>
                    <option value="HDFC">HDFC Bank</option>
                    <option value="ICICI">ICICI Bank</option>
                    <option value="Axis">Axis Bank</option>
                    <option value="BOI">Bank of India</option>
                    <option value="PNB">Punjab National Bank</option>
                </select>
            </div>

            <!-- Date Range -->
            <div>
                <label for="dateRange" class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                <input type="date" 
                       id="dateFromFilter" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                       placeholder="From Date">
            </div>
        </div>

        <div class="mt-4 flex justify-between items-center">
            <button id="clearFilters" class="text-gray-600 hover:text-gray-800 font-medium">
                <i class="fas fa-times mr-1"></i>Clear Filters
            </button>
            <div class="text-sm text-gray-600">
                Showing <span id="recordCount">0</span> results
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 table-hover">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank Details</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody id="payoutTableBody" class="bg-white divide-y divide-gray-200">
                    <!-- Data will be loaded here -->
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                            <div class="loading-spinner mx-auto mb-4"></div>
                            <p>Loading payout requests...</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div id="paginationContainer" class="bg-gray-50 px-6 py-3 border-t border-gray-200">
            <!-- Pagination will be loaded here -->
        </div>
    </div>
</div>

<!-- Withdrawal Details Modal -->
<div id="withdrawalModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Withdrawal Request Details</h3>
                <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        
        <div id="modalContent" class="p-6">
            <!-- Modal content will be loaded here -->
        </div>
    </div>
</div>

<!-- Action Confirmation Modal -->
<div id="actionModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-md w-full">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div id="actionIcon" class="w-12 h-12 rounded-full flex items-center justify-center mr-4">
                    <i class="fas fa-question text-xl text-white"></i>
                </div>
                <div>
                    <h3 id="actionTitle" class="text-lg font-semibold text-gray-900">Confirm Action</h3>
                    <p id="actionMessage" class="text-gray-600">Are you sure you want to proceed?</p>
                </div>
            </div>
            
            <div id="notesSection" class="mb-4 hidden">
                <label for="actionNotes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                <textarea id="actionNotes" 
                          rows="3" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                          placeholder="Enter notes or reason..."></textarea>
            </div>
            
            <div class="flex justify-end space-x-3">
                <button id="cancelAction" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                    Cancel
                </button>
                <button id="confirmAction" class="px-4 py-2 text-white rounded-lg transition-colors">
                    Confirm
                </button>
            </div>
        </div>
    </div>
</div>
